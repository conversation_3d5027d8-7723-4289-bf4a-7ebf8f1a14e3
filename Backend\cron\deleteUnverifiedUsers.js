const cron = require('node-cron');
const User = require('../models/User');

const deleteUnverifiedUsers = async () => {
    try {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000); // ⏳ 24 hours
        const result = await User.deleteMany({
            'otp.verified': false,
            createdAt: { $lte: oneDayAgo }
        });

        console.log(`🧹 Deleted ${result.deletedCount} unverified users older than 1 day.`);
    } catch (err) {
        console.error('❌ Error deleting unverified users:', err);
    }
};

// ⏱️ Run every hour at minute 0
cron.schedule('0 * * * *', deleteUnverifiedUsers);
