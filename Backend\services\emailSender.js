const nodemailer = require("nodemailer");
const dotenv = require("dotenv");
dotenv.config();

const {
    SMTP_USER,
    SMTP_PASS,
    SMTP_HOST,
    SMTP_PORT,
    APP_NAME,
    APP_URL
} = process.env;

const transporter = nodemailer.createTransport({
    host: SMTP_HOST || "smtp.gmail.com",
    port: SMTP_PORT || 587,
    secure: false,
    auth: {
        user: SMTP_USER,
        pass: SMTP_PASS
    }
});

const LOGO_URL = "https://res.cloudinary.com/dhqwyqekj/image/upload/w_1000,c_fill,ar_1:1,g_auto,r_max,bo_5px_solid_red,b_rgb:262c35/v1750831228/Medini_logo_White-1_1_kfnj8n.png";
const BRAND_NAME = APP_NAME || "College Management System";

function createEmailTemplate({ title, message, buttonUrl, buttonText }) {
    return `
<!DOCTYPE html>
<html>
<body style="margin:0;padding:0;background-color:#f9f9f9;">
<table width="100%" style="font-family: Arial, sans-serif;">
<tr><td align="center">
  <table width="600" style="background-color:#ffffff;border-radius:8px;overflow:hidden;">
    <tr>
      <td align="center" style="padding:30px;">
        <img src="${LOGO_URL}" alt="${BRAND_NAME}" style="width:120px;height:auto;" />
      </td>
    </tr>
    <tr>
      <td align="center" style="padding:0 30px 20px 30px;">
        <h2 style="color:#29354d;font-size:24px;margin:0;">${title}</h2>
      </td>
    </tr>
    <tr>
      <td align="center" style="padding:0 30px;">
        <p style="color:#555;font-size:16px;line-height:1.5;margin:0;">
            ${message}
        </p>
      </td>
    </tr>
    ${buttonUrl && buttonText ? `
    <tr>
      <td align="center" style="padding:30px;">
        <a href="${buttonUrl}" style="
            background-color:#fcc250;
            color:#29354d;
            font-weight:bold;
            font-size:16px;
            text-decoration:none;
            padding:12px 30px;
            border-radius:6px;
            display:inline-block;">
            ${buttonText}
        </a>
      </td>
    </tr>` : ""}
    <tr>
      <td align="center" style="padding:30px;">
        <hr style="border:none;border-bottom:1px solid #ccc;" />
        <p style="font-size:14px;color:#555;text-align:center;">
            Thanks,<br/>The ${BRAND_NAME} Team
        </p>
      </td>
    </tr>
  </table>
</td></tr>
</table>
</body>
</html>`;
}

async function sendEmail({ to, subject, title, message, buttonUrl, buttonText }) {
    const html = createEmailTemplate({ title, message, buttonUrl, buttonText });
    const text = `${title}\n\n${message}${buttonUrl ? `\n\nLink: ${buttonUrl}` : ""}`;

    return transporter.sendMail({
        from: `"${BRAND_NAME} Support" <${SMTP_USER}>`,
        to,
        subject,
        text,
        html
    });
}

// --------------------------------------
// 📬 PREDEFINED EMAIL TYPES
// --------------------------------------

async function sendOtpEmail(toEmail, otp) {
    const verificationLink = `${APP_URL}/verify-otp?email=${encodeURIComponent(toEmail)}&otp=${otp}`;

    return sendEmail({
        to: toEmail,
        subject: "Verify Your Email Address",
        title: "Email Verification Required",
        message: `
      <p>Hello,</p>
      <p>Verify your email by clicking the button or using the OTP below.</p>
      <p><strong>OTP: ${otp}</strong></p>
      <p>This will expire in 10 minutes.</p>`,
        buttonUrl: verificationLink,
        buttonText: "Verify Email"
    });
}

async function sendPasswordResetEmail(toEmail, resetLink) {
    return sendEmail({
        to: toEmail,
        subject: "Password Reset Request",
        title: "Reset Your Password",
        message: "We received a request to reset your password. This link will expire in 1 hour.",
        buttonUrl: resetLink,
        buttonText: "Reset Password"
    });
}

async function sendWelcomeEmail(toEmail, name) {
    return sendEmail({
        to: toEmail,
        subject: `Welcome to ${BRAND_NAME}!`,
        title: `Hello, ${name}!`,
        message: "Thank you for joining our platform. Start exploring by visiting the site.",
        buttonUrl: APP_URL,
        buttonText: "Explore Now"
    });
}

async function sendNotificationEmail(toEmail, subject, message) {
    return sendEmail({
        to: toEmail,
        subject,
        title: subject,
        message
    });
}

module.exports = {
    sendOtpEmail,
    sendPasswordResetEmail,
    sendWelcomeEmail,
    sendNotificationEmail
};

