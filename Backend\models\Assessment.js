const mongoose = require('mongoose');
const assessmentSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true
    },
    type: {
        type: String,
        enum: ['quiz', 'test', 'exam', 'practical', 'viva', 'project'],
        required: true
    },
    description: {
        type: String,
        trim: true
    },
    subject: {
        type: String,
        trim: true
    },
    batch: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch',
        required: true
    },
    maxMarks: {
        type: Number,
        required: true,
        min: 1
    },
    date: {
        type: Date,
        required: true
    },
    duration: {
        type: Number // in minutes
    },
    studentMarks: [{
        student: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        marks: {
            type: Number,
            required: true,
            min: 0
        },
        remarks: {
            type: String,
            trim: true
        },
        grade: {
            type: String,
            trim: true
        }
    }],
    uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    isPublished: {
        type: <PERSON>olean,
        default: false
    }
}, {
    timestamps: true
});

// Virtual for calculating average marks
assessmentSchema.virtual('averageMarks').get(function () {
    if (this.studentMarks.length === 0) return 0;
    const total = this.studentMarks.reduce((sum, mark) => sum + mark.marks, 0);
    return Math.round((total / this.studentMarks.length) * 100) / 100;
});

// Index for efficient queries
assessmentSchema.index({ batch: 1, uploadedBy: 1 });
assessmentSchema.index({ date: 1, type: 1 });
assessmentSchema.index({ 'studentMarks.student': 1 });

module.exports = mongoose.model('Assessment', assessmentSchema);