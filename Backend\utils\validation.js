const Joi = require('joi');

const schemas = {
    // User registration validation
    userRegistration: Joi.object({
        name: Joi.string().min(2).max(50).required(),
        email: Joi.string().email().required(),
        password: Joi.string().min(6).required(),
        role: Joi.string().valid('admin', 'student', 'trainer', 'college').required(),
        college: Joi.string().when('role', {
            is: Joi.string().valid('student', 'trainer'),
            then: Joi.required(),
            otherwise: Joi.optional()
        })
    }),

    // User login validation
    userLogin: Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().required()
    }),

    // Batch creation validation
    batchCreation: Joi.object({
        name: Joi.string().min(2).max(100).required(),
        college: Joi.string().required(),
        trainer: Joi.string().required(),
        startDate: Joi.date().min('now').required(),
        endDate: Joi.date().min(Joi.ref('startDate')).required(),
        capacity: Joi.number().integer().min(1).max(100).required(),
        description: Joi.string().max(500).optional()
    }),

    // Feedback submission validation
    feedbackSubmission: Joi.object({
        rating: Joi.number().integer().min(1).max(5).required(),
        comment: Joi.string().max(1000).required(),
        batchId: Joi.string().required()
    })
};

const validateRequest = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body);
        if (error) {
            return res.status(400).json({
                success: false,
                error: error.details[0].message
            });
        }
        next();
    };
};

module.exports = { schemas, validateRequest };
