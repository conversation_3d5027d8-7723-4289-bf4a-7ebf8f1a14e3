// middlewares/validationMiddleware.js (Additional validations for trainer)
const { body, param, query, validationResult } = require('express-validator');
const ApiError = require('../utils/ApiError');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        throw new ApiError(400, 'Validation failed', errorMessages);
    }
    next();
};

// Course Material Validation
const validateCreateMaterial = [
    body('title')
        .trim()
        .notEmpty()
        .withMessage('Material title is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Title must be between 3 and 100 characters'),
    
    body('description')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Description cannot exceed 500 characters'),
    
    body('type')
        .optional()
        .isIn(['document', 'presentation', 'video', 'audio', 'image', 'other'])
        .withMessage('Invalid material type'),
    
    param('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),
    
    handleValidationErrors
];

// Assignment Validation
const validateCreateAssignment = [
    body('title')
        .trim()
        .notEmpty()
        .withMessage('Assignment title is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Title must be between 3 and 100 characters'),
    
    body('description')
        .trim()
        .notEmpty()
        .withMessage('Assignment description is required')
        .isLength({ min: 10, max: 1000 })
        .withMessage('Description must be between 10 and 1000 characters'),
    
    body('dueDate')
        .isISO8601()
        .withMessage('Invalid due date format')
        .custom(value => {
            const dueDate = new Date(value);
            const now = new Date();
            if (dueDate <= now) {
                throw new Error('Due date must be in the future');
            }
            return true;
        }),
    
    body('maxMarks')
        .isInt({ min: 1, max: 1000 })
        .withMessage('Maximum marks must be between 1 and 1000'),
    
    body('instructions')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Instructions cannot exceed 1000 characters'),
    
    body('subject')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Subject cannot exceed 50 characters'),
    
    param('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),
    
    handleValidationErrors
];

// Attendance Validation
const validateAttendance = [
    body('date')
        .isISO8601()
        .withMessage('Invalid date format')
        .custom(value => {
            const attendanceDate = new Date(value);
            const now = new Date();
            const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
            
            if (attendanceDate > now) {
                throw new Error('Cannot mark attendance for future dates');
            }
            if (attendanceDate < thirtyDaysAgo) {
                throw new Error('Cannot mark attendance for dates older than 30 days');
            }
            return true;
        }),
    
    body('studentAttendance')
        .isArray({ min: 1 })
        .withMessage('Student attendance data is required'),
    
    body('studentAttendance.*.studentId')
        .isMongoId()
        .withMessage('Invalid student ID'),
    
    body('studentAttendance.*.status')
        .isBoolean()
        .withMessage('Attendance status must be true or false'),
    
    param('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),
    
    handleValidationErrors
];

// Assessment Validation
const validateAssessment = [
    body('title')
        .trim()
        .notEmpty()
        .withMessage('Assessment title is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Title must be between 3 and 100 characters'),
    
    body('type')
        .isIn(['quiz', 'test', 'exam', 'practical', 'viva', 'project'])
        .withMessage('Invalid assessment type'),
    
    body('maxMarks')
        .isInt({ min: 1, max: 1000 })
        .withMessage('Maximum marks must be between 1 and 1000'),
    
    body('date')
        .isISO8601()
        .withMessage('Invalid date format'),
    
    body('description')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Description cannot exceed 500 characters'),
    
    body('subject')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Subject cannot exceed 50 characters'),
    
    body('duration')
        .optional()
        .isInt({ min: 1, max: 600 })
        .withMessage('Duration must be between 1 and 600 minutes'),
    
    param('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),
    
    handleValidationErrors
];

// Grade Submission Validation
const validateGradeSubmission = [
    body('marks')
        .isFloat({ min: 0 })
        .withMessage('Marks must be a positive number'),
    
    body('feedback')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Feedback cannot exceed 500 characters'),
    
    param('assignmentId')
        .isMongoId()
        .withMessage('Invalid assignment ID'),
    
    param('submissionId')
        .isMongoId()
        .withMessage('Invalid submission ID'),
    
    handleValidationErrors
];

// Upload Assessment Marks Validation
const validateUploadMarks = [
    body('studentMarks')
        .isArray({ min: 1 })
        .withMessage('Student marks data is required'),
    
    body('studentMarks.*.studentId')
        .isMongoId()
        .withMessage('Invalid student ID'),
    
    body('studentMarks.*.marks')
        .isFloat({ min: 0 })
        .withMessage('Marks must be a positive number'),
    
    body('studentMarks.*.remarks')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Remarks cannot exceed 200 characters'),
    
    param('assessmentId')
        .isMongoId()
        .withMessage('Invalid assessment ID'),
    
    handleValidationErrors
];

// Notification Validation
const validateNotification = [
    body('title')
        .trim()
        .notEmpty()
        .withMessage('Notification title is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Title must be between 3 and 100 characters'),
    
    body('message')
        .trim()
        .notEmpty()
        .withMessage('Notification message is required')
        .isLength({ min: 10, max: 500 })
        .withMessage('Message must be between 10 and 500 characters'),
    
    body('type')
        .optional()
        .isIn(['general', 'assignment', 'material', 'assessment', 'grade', 'attendance', 'announcement'])
        .withMessage('Invalid notification type'),
    
    body('batchId')
        .optional()
        .isMongoId()
        .withMessage('Invalid batch ID'),
    
    body('recipients')
        .optional()
        .isArray()
        .withMessage('Recipients must be an array'),
    
    body('recipients.*')
        .optional()
        .isMongoId()
        .withMessage('Invalid recipient ID'),
    
    body('priority')
        .optional()
        .isIn(['low', 'medium', 'high'])
        .withMessage('Invalid priority level'),
    
    handleValidationErrors
];

// Batch ID Parameter Validation
const validateBatchId = [
    param('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),
    handleValidationErrors
];

// Student ID Parameter Validation
const validateStudentId = [
    param('studentId')
        .isMongoId()
        .withMessage('Invalid student ID'),
    handleValidationErrors
];

// Assignment ID Parameter Validation
const validateAssignmentId = [
    param('assignmentId')
        .isMongoId()
        .withMessage('Invalid assignment ID'),
    handleValidationErrors
];

// Material ID Parameter Validation
const validateMaterialId = [
    param('materialId')
        .isMongoId()
        .withMessage('Invalid material ID'),
    handleValidationErrors
];

// Assessment ID Parameter Validation
const validateAssessmentId = [
    param('assessmentId')
        .isMongoId()
        .withMessage('Invalid assessment ID'),
    handleValidationErrors
];

// Date Range Query Validation
const validateDateRange = [
    query('startDate')
        .optional()
        .isISO8601()
        .withMessage('Invalid start date format'),
    
    query('endDate')
        .optional()
        .isISO8601()
        .withMessage('Invalid end date format')
        .custom((value, { req }) => {
            if (req.query.startDate && value) {
                const startDate = new Date(req.query.startDate);
                const endDate = new Date(value);
                if (endDate < startDate) {
                    throw new Error('End date must be after start date');
                }
            }
            return true;
        }),
    
    handleValidationErrors
];

// Pagination Query Validation
const validatePagination = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    
    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    
    handleValidationErrors
];

// Profile Update Validation
const validateProfileUpdate = [
    body('firstName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    
    body('lastName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    
    body('mobile')
        .optional()
        .isMobilePhone('any')
        .withMessage('Invalid mobile number'),
    
    body('qualification')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Qualification cannot exceed 100 characters'),
    
    body('experience')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Experience cannot exceed 200 characters'),
    
    body('otherDetails')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Other details cannot exceed 500 characters'),
    
    // Prevent updating sensitive fields
    body('password').not().exists().withMessage('Password cannot be updated via this endpoint'),
    body('role').not().exists().withMessage('Role cannot be updated'),
    body('userId').not().exists().withMessage('User ID cannot be updated'),
    body('assignedBatches').not().exists().withMessage('Assigned batches cannot be updated via this endpoint'),
    
    handleValidationErrors
];

module.exports = {
    validateCreateMaterial,
    validateCreateAssignment,
    validateAttendance,
    validateAssessment,
    validateGradeSubmission,
    validateUploadMarks,
    validateNotification,
    validateBatchId,
    validateStudentId,
    validateAssignmentId,
    validateMaterialId,
    validateAssessmentId,
    validateDateRange,
    validatePagination,
    validateProfileUpdate,
    handleValidationErrors
};