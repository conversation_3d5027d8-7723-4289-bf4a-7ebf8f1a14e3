const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const { protect } = require('../middlewares/authMiddleware');
const roleMiddleware = require('../middlewares/roleMiddleware');
const { uploadMiddleware } = require('../middlewares/fileUpload');
const {
    validateCreateStudent,
    validateCreateTrainer,
    validateCreateCollege,
    validateCreateBatch,
    validateAssignBatch,
    validateUploadCourse,
    validateUploadCertificate,
    validateCreateCourse
} = require('../middlewares/validation');

// Apply authentication and admin role middleware to all routes
router.use(protect, roleMiddleware('admin'));


//! COurces management

router.post(
    '/courses/create',
    // uploadMiddleware.courseMaterial.single('courseFile'),
    validateCreateCourse,
    adminController.createCourse
);
router.get('/courses', adminController.getAllCourses);
// router.get('/courses/:courseId', adminController.getCourseDetails);
// User Management
router.post('/create-student', validateCreateStudent, adminController.createStudent);
router.post('/create-trainer', validateCreateTrainer, adminController.createTrainer);
router.post('/create-college', validateCreateCollege, adminController.createCollege);
router.get('/users', adminController.getAllUsers); -//!<----------------- This function need to implementted
    router.get('/report/user/:userId', adminController.getUserReport);//! <----------------- This function need to implementted
// router.patch('/user/:userId/toggle-status', adminController.toggleUserStatus);//!< ----------------- This function need to implementted
// router.patch('/user/:userId', adminController.updateUser);//!<----------------- This function need to implementted
router.delete('/user/:userId', adminController.deleteUser);//!<----------------- This function need to implementted

// Batch Management
router.post('/create-batch', validateCreateBatch, adminController.createBatch);
router.post('/assign-batch', validateAssignBatch, adminController.assignBatchToTrainer);
router.get('/batches', adminController.getAllBatches);
router.get('/courses/:courseId/batches', adminController.getBatchesByCourse);
router.get('/report/batch/:batchId', adminController.getBatchReport);//!<----------------- This function need to implementted
// router.patch('/batch/:batchId/status', adminController.updateBatchStatus);//!<----------------- This function need to implementted
// router.delete('/batch/:batchId/student/:studentId', adminController.removeStudentFromBatch);//!<----------------- This function need to implementted

// Course Management
router.post(
    '/upload-course',
    uploadMiddleware.courseMaterial.single('courseFile'),
    adminController.uploadCourse
);
router.get('/courses', adminController.getAllCourses);

// // Certificate Management
router.post(
    '/upload-certificate',
    uploadMiddleware.certificate.single('certificateFile'),
    adminController.uploadCertificate
);
// Dashboard
// router.get('/dashboard/stats', adminController.getDashboardStats);
// router.get('/dashboard/recent-activities', adminController.getRecentActivities);

// Bulk Operations
// router.post('/bulk/create-students', upload.single('csvFile'), adminController.bulkCreateStudents);
router.post('/bulk/assign-students', adminController.bulkAssignStudents);

// Reports and Analytics
// router.get('/report/department-wise', adminController.getDepartmentWiseReport);
// router.get('/report/batch-performance', adminController.getBatchPerformanceReport);
// router.get('/report/monthly-registrations', adminController.getMonthlyRegistrationReport);

module.exports = router;