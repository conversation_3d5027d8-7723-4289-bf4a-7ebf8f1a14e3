const mongoose = require('mongoose');

const collegeSchema = new mongoose.Schema({
    collegeId: {
        type: String,
        required: true,
        unique: true
    },
    collegeCode: {                      // ✅ Add this
        type: String,
        required: true,
        unique: true                    // ✅ Ensure uniqueness
    },
    collegeName: {
        type: String,
        required: true
    },
    adminId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    departments: [{
        type: String
    }],
    students: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    downloads: [{
        type: String
    }],
    createdAt: {
        type: Date,
        default: Date.now
    },
    isActive: {                         // ✅ Optional: useful if you need to disable a college
        type: Boolean,
        default: true
    },
    createdBy: {                        // ✅ Track who created it
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
});

module.exports = mongoose.model('College', collegeSchema);

