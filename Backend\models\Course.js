const mongoose = require('mongoose');

const courseSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true,
        minlength: 3,
        maxlength: 100
    },
    courseCode: {
        type: String,
        required: true,
        unique: true,
        uppercase: true,
        trim: true,
        match: /^[A-Z0-9]+$/,
        minlength: 2,
        maxlength: 10
    },
    description: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    syllabus: {
        type: String,
        trim: true
    },
    duration: {
        type: String, // e.g., "6 weeks", "3 months"
        required: true,
        trim: true
    },
    department: {
        type: String, // Optional: e.g., "CSE", "ECE"
        trim: true,
        maxlength: 50
    },
    difficulty: {
        type: String,
        enum: ['beginner', 'intermediate', 'advanced'],
        default: 'intermediate'
    },
    prerequisites: [{
        type: String,
        trim: true
    }],
    objectives: [{
        type: String,
        trim: true
    }],
    modules: [{
        title: {
            type: String,
            required: true,
            trim: true
        },
        description: {
            type: String,
            trim: true
        },
        duration: {
            type: String,
            trim: true
        }
    }],
    materials: [{
        title: {
            type: String,
            required: true,
            trim: true
        },
        type: {
            type: String,
            enum: ['pdf', 'video', 'document', 'other'],
            default: 'document'
        },
        fileUrl: {
            type: String,
            required: true
        },
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    isActive: {
        type: Boolean,
        default: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Admin or Trainer
        required: true
    }
}, {
    timestamps: true
});

// Indexes for better performance
courseSchema.index({ courseCode: 1 });
courseSchema.index({ title: 'text', description: 'text' });
courseSchema.index({ department: 1 });
courseSchema.index({ difficulty: 1 });
courseSchema.index({ isActive: 1 });

module.exports = mongoose.model('Course', courseSchema);

