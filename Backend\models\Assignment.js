const mongoose = require('mongoose');

const assignmentSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        required: true,
        trim: true
    },
    instructions: {
        type: String,
        trim: true
    },
    batch: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch',
        required: true
    },
    fileUrl: {
        type: String // Optional - for assignment document/instructions
    },
    fileName: {
        type: String
    },
    dueDate: {
        type: Date,
        required: true
    },
    maxMarks: {
        type: Number,
        required: true,
        min: 1
    },
    subject: {
        type: String,
        trim: true
    },
    uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    allowLateSubmission: {
        type: Boolean,
        default: false
    },
    lateSubmissionPenalty: {
        type: Number,
        min: 0,
        max: 100,
        default: 0 // Percentage penalty
    }
}, {
    timestamps: true
});

// Virtual for checking if assignment is overdue
assignmentSchema.virtual('isOverdue').get(function () {
    return new Date() > this.dueDate;
});

// Index for efficient queries
assignmentSchema.index({ batch: 1, uploadedBy: 1 });
assignmentSchema.index({ dueDate: 1, isActive: 1 });

module.exports = mongoose.model('Assignment', assignmentSchema);
