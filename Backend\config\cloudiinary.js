const cloudinary = require('cloudinary').v2;
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const multer = require('multer');
const path = require('path');
const logger = require('../utils/logger');

// Configure Cloudinary
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
    secure: true
});

// Storage configuration for different file types
const createStorage = (folder, allowedFormats) => {
    return new CloudinaryStorage({
        cloudinary: cloudinary,
        params: {
            folder: `college-management/${folder}`,
            allowed_formats: allowedFormats,
            resource_type: 'auto',
        },
    });
};

// Different storage configurations
const storageConfigs = {
    // Profile pictures
    profilePictures: createStorage('profile-pictures', ['jpg', 'png', 'jpeg', 'gif']),

    // Course materials
    courseMaterials: createStorage('course-materials', ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt']),

    // Assignment submissions
    assignments: createStorage('assignments', ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar']),

    // Certificates
    certificates: createStorage('certificates', ['pdf', 'png', 'jpg', 'jpeg']),

    // College documents
    documents: createStorage('documents', ['pdf', 'doc', 'docx', 'jpg', 'png', 'jpeg']),

    // General uploads
    general: createStorage('general', ['jpg', 'png', 'jpeg', 'gif', 'pdf', 'doc', 'docx', 'txt'])
};

// File size limits (in bytes)
const fileSizeLimits = {
    image: 5 * 1024 * 1024, // 5MB
    document: 10 * 1024 * 1024, // 10MB
    video: 50 * 1024 * 1024, // 50MB
    general: 10 * 1024 * 1024 // 10MB
};

// Create multer upload middleware
const createUploadMiddleware = (storageType, fileSize = fileSizeLimits.general) => {
    const storage = storageConfigs[storageType] || storageConfigs.general;

    return multer({
        storage: storage,
        limits: {
            fileSize: fileSize,
            files: 10 // Maximum 10 files
        },
        fileFilter: (req, file, cb) => {
            try {
                // Check file type based on mimetype
                const allowedTypes = {
                    'image/jpeg': ['jpg', 'jpeg'],
                    'image/png': ['png'],
                    'image/gif': ['gif'],
                    'application/pdf': ['pdf'],
                    'application/msword': ['doc'],
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
                    'application/vnd.ms-powerpoint': ['ppt'],
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['pptx'],
                    'text/plain': ['txt'],
                    'application/zip': ['zip'],
                    'application/x-rar-compressed': ['rar']
                };

                if (allowedTypes[file.mimetype]) {
                    cb(null, true);
                } else {
                    cb(new Error(`File type ${file.mimetype} not allowed`), false);
                }
            } catch (error) {
                cb(error, false);
            }
        }
    });
};

// Upload middleware instances
const uploadMiddleware = {
    profilePicture: createUploadMiddleware('profilePictures', fileSizeLimits.image),
    courseMaterial: createUploadMiddleware('courseMaterials', fileSizeLimits.document),
    assignment: createUploadMiddleware('assignments', fileSizeLimits.document),
    certificate: createUploadMiddleware('certificates', fileSizeLimits.document),
    document: createUploadMiddleware('documents', fileSizeLimits.document),
    general: createUploadMiddleware('general', fileSizeLimits.general)
};

// Utility functions
const uploadToCloudinary = async (filePath, folder, options = {}) => {
    try {
        const result = await cloudinary.uploader.upload(filePath, {
            folder: `college-management/${folder}`,
            resource_type: 'auto',
            ...options
        });

        return {
            url: result.secure_url,
            publicId: result.public_id,
            format: result.format,
            size: result.bytes
        };
    } catch (error) {
        logger.error('Cloudinary upload error:', error);
        throw new Error('File upload failed');
    }
};

const deleteFromCloudinary = async (publicId) => {
    try {
        const result = await cloudinary.uploader.destroy(publicId);
        return result.result === 'ok';
    } catch (error) {
        logger.error('Cloudinary delete error:', error);
        return false;
    }
};

// Generate signed URL for secure access
const generateSignedUrl = (publicId, options = {}) => {
    try {
        return cloudinary.url(publicId, {
            sign_url: true,
            secure: true,
            ...options
        });
    } catch (error) {
        logger.error('Error generating signed URL:', error);
        return null;
    }
};

// Get file info from Cloudinary
const getFileInfo = async (publicId) => {
    try {
        const result = await cloudinary.api.resource(publicId);
        return {
            publicId: result.public_id,
            format: result.format,
            size: result.bytes,
            width: result.width,
            height: result.height,
            createdAt: result.created_at,
            url: result.secure_url
        };
    } catch (error) {
        logger.error('Error getting file info:', error);
        return null;
    }
};

// Test Cloudinary connection
const testConnection = async () => {
    try {
        const result = await cloudinary.api.ping();
        logger.info('Cloudinary connection successful');
        return result;
    } catch (error) {
        logger.error('Cloudinary connection failed:', error);
        throw error;
    }
};

module.exports = {
    cloudinary,
    uploadMiddleware,
    uploadToCloudinary,
    deleteFromCloudinary,
    generateSignedUrl,
    getFileInfo,
    testConnection,
    fileSizeLimits
};