// middlewares/validation.js
const { body, validationResult } = require('express-validator');
const ApiError = require('../utils/ApiError');

// Middleware to handle validation errors
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        throw new ApiError(400, `Validation failed: ${errorMessages.join(', ')}`);
    }
    next();
};

// Student validation
const validateCreateCourse = [
    body('title')
        .notEmpty()
        .withMessage('Course title is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Course title must be between 3 and 100 characters'),

    body('courseCode')
        .notEmpty()
        .withMessage('Course code is required')
        .isLength({ min: 2, max: 10 })
        .withMessage('Course code must be between 2 and 10 characters')
        .matches(/^[A-Z0-9]+$/)
        .withMessage('Course code must contain only uppercase letters and numbers')
        .custom(async (courseCode) => {
            const Course = require('../models/Course');
            const existingCourse = await Course.findOne({
                courseCode: courseCode.toUpperCase()
            });
            if (existingCourse) {
                throw new Error('Course code already exists');
            }
            return true;
        }),

    body('duration')
        .notEmpty()
        .withMessage('Course duration is required'),

    body('description')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Description cannot exceed 1000 characters'),

    body('department')
        .optional()
        .isLength({ max: 50 })
        .withMessage('Department name cannot exceed 50 characters'),

    body('difficulty')
        .optional()
        .isIn(['beginner', 'intermediate', 'advanced'])
        .withMessage('Difficulty must be beginner, intermediate, or advanced'),

    body('prerequisites')
        .optional()
        .isArray()
        .withMessage('Prerequisites must be an array'),

    body('objectives')
        .optional()
        .isArray()
        .withMessage('Objectives must be an array'),

    handleValidationErrors
];
const validateCreateStudent = [
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
];

// Trainer validation
const validateCreateTrainer = [
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('email').isEmail().withMessage('Valid email is required'),
];

// College validation
const validateCreateCollege = [
    body('collegeName').notEmpty().withMessage('College name is required'),
    body('location').notEmpty().withMessage('Location is required'),
];

// Batch validation
const validateCreateBatch = [
    body('batchName')
        .notEmpty()
        .withMessage('Batch name is required')
        .isLength({ min: 2, max: 100 })
        .withMessage('Batch name must be between 2 and 100 characters')
        .trim(),

    body('courseId')
        .notEmpty()
        .withMessage('Course ID is required')
        .isMongoId()
        .withMessage('Course ID must be a valid MongoDB ObjectId')
        .custom(async (courseId) => {
            const Course = require('../models/Course');
            const course = await Course.findById(courseId);
            if (!course) {
                throw new Error('Course not found');
            }
            if (!course.isActive) {
                throw new Error('Cannot create batch for inactive course');
            }
            return true;
        }),

    body('startDate')
        .notEmpty()
        .withMessage('Start date is required')
        .isISO8601()
        .withMessage('Start date must be a valid date')
        .custom((value) => {
            const startDate = new Date(value);
            const now = new Date();
            if (startDate <= now) {
                throw new Error('Start date must be in the future');
            }
            return true;
        }),

    body('endDate')
        .notEmpty()
        .withMessage('End date is required')
        .isISO8601()
        .withMessage('End date must be a valid date')
        .custom((value, { req }) => {
            const endDate = new Date(value);
            const startDate = new Date(req.body.startDate);
            if (endDate <= startDate) {
                throw new Error('End date must be after start date');
            }
            return true;
        }),

    body('maxStudents')
        .notEmpty()
        .withMessage('Maximum students is required')
        .isInt({ min: 1, max: 100 })
        .withMessage('Maximum students must be between 1 and 100'),

    body('location')
        .optional()
        .isLength({ max: 100 })
        .withMessage('Location cannot exceed 100 characters')
        .trim(),

    body('schedule')
        .optional()
        .isLength({ max: 200 })
        .withMessage('Schedule cannot exceed 200 characters')
        .trim(),

    body('description')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Description cannot exceed 500 characters')
        .trim(),

    body('department')
        .optional()
        .isLength({ max: 50 })
        .withMessage('Department cannot exceed 50 characters')
        .trim(),

    body('collegeId')
        .optional()
        .isMongoId()
        .withMessage('College ID must be a valid MongoDB ObjectId')
        .custom(async (collegeId) => {
            if (!collegeId) return true; // Optional field
            const College = require('../models/College');
            const college = await College.findById(collegeId);
            if (!college) {
                throw new Error('College not found');
            }
            if (!college.isActive) {
                throw new Error('Cannot create batch for inactive college');
            }
            return true;
        }),

    handleValidationErrors
];

// Batch assignment validation
const validateAssignBatch = [
    body('trainerId').notEmpty().withMessage('Trainer ID is required'),
    body('batchId').notEmpty().withMessage('Batch ID is required'),
];

// Course upload validation
const validateUploadCourse = [
    body('courseName').notEmpty().withMessage('Course name is required'),
    body('courseCode').notEmpty().withMessage('Course code is required'),
    body('duration').notEmpty().withMessage('Course duration is required'),
];

// Certificate upload validation
const validateUploadCertificate = [
    body('studentId').notEmpty().withMessage('Student ID is required'),
    body('batchId').notEmpty().withMessage('Batch ID is required'),
    body('courseId').notEmpty().withMessage('Course ID is required'),
    body('certificateType').notEmpty().withMessage('Certificate type is required'),
];

module.exports = {
    validateCreateStudent,
    validateCreateTrainer,
    validateCreateCollege,
    validateCreateBatch,
    validateAssignBatch,
    validateUploadCourse,
    validateUploadCertificate,
    validateCreateCourse,
    handleValidationErrors
};
