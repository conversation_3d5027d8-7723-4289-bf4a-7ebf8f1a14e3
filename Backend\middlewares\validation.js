// middlewares/validation.js
const { body } = require('express-validator');

// Student validation
const validateCreateCourse = [
    body('title')
        .notEmpty()
        .withMessage('Course title is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Course title must be between 3 and 100 characters'),

    body('courseCode')
        .notEmpty()
        .withMessage('Course code is required')
        .isLength({ min: 2, max: 10 })
        .withMessage('Course code must be between 2 and 10 characters')
        .matches(/^[A-Z0-9]+$/)
        .withMessage('Course code must contain only uppercase letters and numbers'),

    body('duration')
        .notEmpty()
        .withMessage('Course duration is required'),

    body('description')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Description cannot exceed 1000 characters'),

    body('department')
        .optional()
        .isLength({ max: 50 })
        .withMessage('Department name cannot exceed 50 characters'),

    body('difficulty')
        .optional()
        .isIn(['beginner', 'intermediate', 'advanced'])
        .withMessage('Difficulty must be beginner, intermediate, or advanced'),

    body('prerequisites')
        .optional()
        .isArray()
        .withMessage('Prerequisites must be an array'),

    body('objectives')
        .optional()
        .isArray()
        .withMessage('Objectives must be an array'),
];
const validateCreateStudent = [
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
];

// Trainer validation
const validateCreateTrainer = [
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('email').isEmail().withMessage('Valid email is required'),
];

// College validation
const validateCreateCollege = [
    body('collegeName').notEmpty().withMessage('College name is required'),
    body('location').notEmpty().withMessage('Location is required'),
];

// Batch validation
const validateCreateBatch = [
    body('batchName').notEmpty().withMessage('Batch name is required'),
    body('courseId').notEmpty().withMessage('Course ID is required'),
    body('trainerId').notEmpty().withMessage('Trainer ID is required'),
];

// Batch assignment validation
const validateAssignBatch = [
    body('trainerId').notEmpty().withMessage('Trainer ID is required'),
    body('batchId').notEmpty().withMessage('Batch ID is required'),
];

// Course upload validation
const validateUploadCourse = [
    body('courseName').notEmpty().withMessage('Course name is required'),
    body('courseCode').notEmpty().withMessage('Course code is required'),
    body('duration').notEmpty().withMessage('Course duration is required'),
];

// Certificate upload validation
const validateUploadCertificate = [
    body('studentId').notEmpty().withMessage('Student ID is required'),
    body('batchId').notEmpty().withMessage('Batch ID is required'),
    body('courseId').notEmpty().withMessage('Course ID is required'),
    body('certificateType').notEmpty().withMessage('Certificate type is required'),
];

module.exports = {
    validateCreateStudent,
    validateCreateTrainer,
    validateCreateCollege,
    validateCreateBatch,
    validateAssignBatch,
    validateUploadCourse,
    validateUploadCertificate,
    validateCreateCourse
};
