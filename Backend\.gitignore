# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Logs
logs
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# MongoDB
data/

# Build outputs
build/
dist/
out/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# PM2 process manager
.pm2/

# Docker
docker-compose.override.yml

# SSL certificates
*.pem
*.key
*.crt

# Cache
.cache/
.parcel-cache/

# Session store
sessions/

# Uploaded files (if storing locally)
uploads/
public/uploads/

# Test files
test-results/
coverage/

# Miscellaneous
*.tgz
*.tar.gz