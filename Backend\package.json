{"name": "college-management-backend", "version": "1.0.1", "description": "Training and Certification Management Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"aws-sdk": "^2.1561.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "firebase-admin": "^11.10.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^7.6.0", "morgan": "^1.10.0", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^4.2.1", "nodemailer": "^6.9.4", "pdfkit": "^0.13.0", "winston": "^3.17.0"}, "devDependencies": {"axios": "^1.11.0", "nodemon": "^3.0.3"}}