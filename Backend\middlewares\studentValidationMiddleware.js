const { body, param, query, validationResult } = require('express-validator');
const ApiError = require('../utils/ApiError');

// Helper function to handle validation results
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        throw new ApiError(400, errorMessages.join(', '));
    }
    next();
};

// ================================
// ASSIGNMENT SUBMISSION VALIDATION
// ================================
const validateSubmitAssignment = [
    param('assignmentId')
        .isMongoId()
        .withMessage('Invalid assignment ID'),

    body('comments')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Comments must not exceed 500 characters')
        .trim(),

    // File validation will be handled by multer middleware
    handleValidationErrors
];

// ================================
// FEEDBACK VALIDATION
// ================================
const validateFeedback = [
    body('trainerId')
        .isMongoId()
        .withMessage('Invalid trainer ID'),

    body('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),

    body('rating')
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating must be between 1 and 5'),

    body('comments')
        .optional()
        .isLength({ min: 10, max: 1000 })
        .withMessage('Comments must be between 10 and 1000 characters')
        .trim(),

    handleValidationErrors
];

// ================================
// CERTIFICATE REQUEST VALIDATION
// ================================
const validateCertificateRequest = [
    body('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),

    body('reason')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Reason must not exceed 500 characters')
        .trim(),

    handleValidationErrors
];

// ================================
// PROFILE UPDATE VALIDATION
// ================================
const validateProfileUpdate = [
    body('firstName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('First name can only contain letters and spaces')
        .trim(),

    body('lastName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('Last name can only contain letters and spaces')
        .trim(),

    body('mobile')
        .optional()
        .isMobilePhone('en-IN')
        .withMessage('Please provide a valid Indian mobile number'),

    body('email')
        .optional()
        .isEmail()
        .withMessage('Please provide a valid email address')
        .normalizeEmail(),

    body('dateOfBirth')
        .optional()
        .isISO8601()
        .withMessage('Please provide a valid date of birth')
        .custom((value) => {
            const dob = new Date(value);
            const age = new Date().getFullYear() - dob.getFullYear();
            if (age < 16 || age > 65) {
                throw new Error('Age must be between 16 and 65 years');
            }
            return true;
        }),

    body('collegeName')
        .optional()
        .isLength({ min: 2, max: 100 })
        .withMessage('College name must be between 2 and 100 characters')
        .trim(),

    body('department')
        .optional()
        .isIn(['CSE', 'ECE', 'EEE', 'ME', 'CE', 'IT', 'OTHER'])
        .withMessage('Invalid department'),

    body('semester')
        .optional()
        .isInt({ min: 1, max: 8 })
        .withMessage('Semester must be between 1 and 8'),

    body('passingYear')
        .optional()
        .isInt({ min: 2020, max: new Date().getFullYear() + 4 })
        .withMessage('Invalid passing year'),

    body('preferredLocation')
        .optional()
        .isIn(['Vijaynagar', 'Gulbarga', 'Belgavi', 'Other'])
        .withMessage('Invalid preferred location'),

    // Prevent updating sensitive fields
    body('password').not().exists().withMessage('Password cannot be updated via this endpoint'),
    body('role').not().exists().withMessage('Role cannot be updated'),
    body('userId').not().exists().withMessage('User ID cannot be updated'),
    body('batchId').not().exists().withMessage('Batch ID cannot be updated via this endpoint'),

    handleValidationErrors
];

module.exports = {
    validateSubmitAssignment,
    validateFeedback,
    validateCertificateRequest,
    validateProfileUpdate,
    handleValidationErrors
};
