// config/db.js
const mongoose = require('mongoose');
const logger = require('../utils/logger');

const getConnectionOptions = () => {
    const baseOptions = {
        // Connection Management
        maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE) || 10,
        minPoolSize: parseInt(process.env.DB_MIN_POOL_SIZE) || 5,
        maxIdleTimeMS: parseInt(process.env.DB_MAX_IDLE_TIME) || 30000,
        serverSelectionTimeoutMS: parseInt(process.env.DB_SERVER_SELECTION_TIMEOUT) || 5000,
        socketTimeoutMS: parseInt(process.env.DB_SOCKET_TIMEOUT) || 45000,
        connectTimeoutMS: parseInt(process.env.DB_CONNECT_TIMEOUT) || 10000,

        // Buffering
        bufferCommands: false,

        // Heartbeat
        heartbeatFrequencyMS: parseInt(process.env.DB_HEARTBEAT_FREQUENCY) || 10000,

        // Write Concern
        w: 'majority',
        wtimeoutMS: parseInt(process.env.DB_WRITE_TIMEOUT) || 5000,

        // Read Preference
        readPreference: 'primaryPreferred',

        // Compression
        compressors: ['zlib'],

        // Application Name for monitoring
        appName: process.env.APP_NAME || 'college-management-system',

        // Auto Index
        autoIndex: process.env.NODE_ENV !== 'production',

        // Family preference for IP resolution
        family: 4,
    };

    // Production-specific options
    if (process.env.NODE_ENV === 'production') {
        return {
            ...baseOptions,
            // SSL/TLS Configuration
            ssl: true,
            sslValidate: true,
            sslCA: process.env.DB_SSL_CA,
            sslCert: process.env.DB_SSL_CERT,
            sslKey: process.env.DB_SSL_KEY,
            sslPass: process.env.DB_SSL_PASS,

            // Authentication
            authSource: process.env.DB_AUTH_SOURCE || 'admin',
            authMechanism: process.env.DB_AUTH_MECHANISM || 'SCRAM-SHA-256',

            // Replica Set
            replicaSet: process.env.DB_REPLICA_SET,

            // Read Concern
            readConcern: { level: 'majority' },

            // Retry Writes
            retryWrites: true,
            retryReads: true,
        };
    }

    return baseOptions;
};

const buildConnectionString = () => {
    const {
        DB_HOST = 'localhost',
        DB_PORT = '27017',
        DB_NAME = 'college_management',
        DB_USERNAME,
        DB_PASSWORD,
        MONGO_URI
    } = process.env;

    // ! Use full URI if provided
    if (MONGO_URI) {
        return MONGO_URI;
    }

    // !Build URI with credentials
    if (DB_USERNAME && DB_PASSWORD) {
        // URL encode credentials to handle special characters
        const encodedUsername = encodeURIComponent(DB_USERNAME);
        const encodedPassword = encodeURIComponent(DB_PASSWORD);

        return `mongodb://${encodedUsername}:${encodedPassword}@${DB_HOST}:${DB_PORT}/${DB_NAME}`;
    }

    //* Local development without auth
    return `mongodb://${DB_HOST}:${DB_PORT}/${DB_NAME}`;
};

//! Validate connection string
const validateConnectionString = (connectionString) => {
    if (!connectionString) {
        throw new Error('Database connection string is required');
    }

    // Basic validation
    if (!connectionString.startsWith('mongodb://') && !connectionString.startsWith('mongodb+srv://')) {
        throw new Error('Invalid MongoDB connection string format');
    }

    // Check for required database name
    if (!connectionString.includes('/') || connectionString.endsWith('/')) {
        throw new Error('Database name is required in connection string');
    }

    return true;
};

// Connection retry logic
const connectWithRetry = async (connectionString, options, maxRetries = 5) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            logger.info(`Database connection attempt ${attempt}/${maxRetries}`);

            const connection = await mongoose.connect(connectionString, options);

            logger.info(' Database connected successfully');
            logger.info(` Database: ${connection.connection.name}`);
            logger.info(`Host: ${connection.connection.host}:${connection.connection.port}`);

            return connection;
        } catch (error) {
            logger.error(`❌ Database connection attempt ${attempt} failed:`, error.message);

            if (attempt === maxRetries) {
                throw new Error(`Failed to connect to database after ${maxRetries} attempts: ${error.message}`);
            }

            // Exponential backoff
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
            logger.info(`⏳ Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
};

// Connection event handlers
const setupEventHandlers = () => {
    // Connection events
    mongoose.connection.on('connected', () => {
        logger.info('<+> Mongoose connected to database');
    });

    mongoose.connection.on('error', (err) => {
        logger.error('<-> Mongoose connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
        logger.warn(' ---Mongoose disconnected from database');
    });

    mongoose.connection.on('reconnected', () => {
        logger.info(' Mongoose reconnected to database');
    });

    mongoose.connection.on('timeout', () => {
        logger.error(' Mongoose connection timeout');
    });

    mongoose.connection.on('close', () => {
        logger.info(' Mongoose connection closed');
    });

    // Process termination handlers
    process.on('SIGINT', async () => {
        try {
            await mongoose.connection.close();
            logger.info(' Database connection closed due to app termination');
            process.exit(0);
        } catch (error) {
            logger.error('Error closing database connection:', error);
            process.exit(1);
        }
    });

    process.on('SIGTERM', async () => {
        try {
            await mongoose.connection.close();
            logger.info(' Database connection closed due to app termination');
            process.exit(0);
        } catch (error) {
            logger.error('Error closing database connection:', error);
            process.exit(1);
        }
    });
};

// Health check function
const checkDatabaseHealth = async () => {
    try {
        await mongoose.connection.db.admin().ping();
        return {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            readyState: mongoose.connection.readyState,
            host: mongoose.connection.host,
            port: mongoose.connection.port,
            name: mongoose.connection.name
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString(),
            readyState: mongoose.connection.readyState
        };
    }
};

// Main connection function
const connectDB = async () => {
    try {
        // Validate environment
        if (!process.env.NODE_ENV) {
            throw new Error('NODE_ENV environment variable is required');
        }

        // Build and validate connection string
        const connectionString = buildConnectionString();
        validateConnectionString(connectionString);

        // Get connection options
        const options = getConnectionOptions();

        // Setup event handlers
        setupEventHandlers();

        // Set mongoose options
        mongoose.set('strictQuery', true);
        mongoose.set('sanitizeFilter', true);

        // Connect with retry logic
        await connectWithRetry(connectionString, options);

        // Log connection info (without sensitive data)
        logger.info('>>>>Connection Configuration:');
        logger.info(`>>>>Environment: ${process.env.NODE_ENV}`);
        logger.info(`>>>>Pool Size: ${options.maxPoolSize}`);
        logger.info(`>>>>SSL: ${options.ssl || false}`);
        logger.info(`>>>>Auth Source: ${options.authSource || 'default'}`);
        logger.info(`>>>>Replica Set: ${options.replicaSet || 'none'}`);

        return mongoose.connection;
    } catch (error) {
        logger.error(' Database connection failed:', error.message);
        throw error;
    }
};

// Graceful disconnection
const disconnectDB = async () => {
    try {
        await mongoose.connection.close();
        logger.info('Database disconnected gracefully');
    } catch (error) {
        logger.error('Error during database disconnection:', error);
        throw error;
    }
};

// Export functions
module.exports = {
    connectDB,
    disconnectDB,
    checkDatabaseHealth,
    connection: mongoose.connection
};