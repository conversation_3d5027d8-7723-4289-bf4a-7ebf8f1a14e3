const mongoose = require('mongoose');

const batchSchema = new mongoose.Schema({
    batchName: {
        type: String,
        required: true,
        trim: true,
        minlength: 2,
        maxlength: 100
    },
    batchId: {
        type: String,
        required: true,
        unique: true, // Unique batch code
        trim: true
    },
    courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true // ✅ Now required for proper course-batch association
    },
    trainerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Must be a user with role: "trainer"
        validate: {
            validator: async function(trainerId) {
                if (!trainerId) return true; // Allow null/undefined for now
                const User = mongoose.model('User');
                const trainer = await User.findById(trainerId);
                return trainer && trainer.role === 'trainer';
            },
            message: 'Trainer must have role "trainer"'
        }
    },
    students: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Must be users with role: "student"
        validate: {
            validator: async function(studentId) {
                const User = mongoose.model('User');
                const student = await User.findById(studentId);
                return student && student.role === 'student';
            },
            message: 'All students must have role "student"'
        }
    }],
    maxStudents: {
        type: Number,
        required: true,
        min: 1,
        max: 100
    },
    currentStudents: {
        type: Number,
        default: 0,
        min: 0
    },
    startDate: {
        type: Date,
        required: true,
        validate: {
            validator: function(startDate) {
                return startDate >= new Date();
            },
            message: 'Start date must be in the future'
        }
    },
    endDate: {
        type: Date,
        required: true,
        validate: {
            validator: function(endDate) {
                return endDate > this.startDate;
            },
            message: 'End date must be after start date'
        }
    },
    location: {
        type: String,
        trim: true,
        maxlength: 100
    },
    schedule: {
        type: String,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        trim: true,
        maxlength: 500
    },
    department: {
        type: String,
        trim: true,
        maxlength: 50
    },
    collegeId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'College'
    },
    status: {
        type: String,
        enum: ['upcoming', 'ongoing', 'completed', 'cancelled'],
        default: 'upcoming'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    }
}, {
    timestamps: true
});

// Pre-save middleware to update currentStudents count
batchSchema.pre('save', function(next) {
    if (this.students) {
        this.currentStudents = this.students.length;
    }
    next();
});

// Validation to ensure currentStudents doesn't exceed maxStudents
batchSchema.pre('save', function(next) {
    if (this.currentStudents > this.maxStudents) {
        next(new Error('Current students count cannot exceed maximum students limit'));
    } else {
        next();
    }
});

// Indexes for better performance
batchSchema.index({ batchId: 1 });
batchSchema.index({ courseId: 1 });
batchSchema.index({ trainerId: 1 });
batchSchema.index({ status: 1 });
batchSchema.index({ startDate: 1 });
batchSchema.index({ collegeId: 1 });
batchSchema.index({ isActive: 1 });

// Compound indexes
batchSchema.index({ courseId: 1, status: 1 });
batchSchema.index({ trainerId: 1, status: 1 });

module.exports = mongoose.model('Batch', batchSchema);
