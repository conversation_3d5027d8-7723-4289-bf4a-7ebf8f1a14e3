const mongoose = require('mongoose');

const batchSchema = new mongoose.Schema({
    batchName: {
        type: String,
        required: true
    },
    batchId: {
        type: String,
        required: true,
        unique: true // Unique batch code
    },
    courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        // required: true
    },
    trainerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Must be a user with role: "trainer"
        // required: true
    },
    students: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User' // Must be users with role: "student"
    }],
    startDate: {
        type: Date,
        required: true
    },
    endDate: {
        type: Date,
        required: true
    },
    department: {
        type: String,
        // required: true
    },
    collegeId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'College'
    },
    status: {
        type: String,
        enum: ['ongoing', 'completed', 'upcoming'],
        default: 'upcoming'
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Batch', batchSchema);
