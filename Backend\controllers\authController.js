const User = require('../models/User');
const bcrypt = require('bcryptjs');
const asyncHandler = require('../utils/asyncHandler');
const ApiError = require('../utils/apiError');
const ApiResponse = require('../utils/apiResponse');
const {
    generateTokenPair,
    verifyRefreshToken,
    blacklistToken
} = require('../config/jwt');
const { sendOtpEmail } = require('../services/emailSender');

//!    Register User
exports.register = asyncHandler(async (req, res) => {
    const {
        name, email, password, mobile, whatsapp, dob,
        collegeName, passingYear, department, preferredLocation, semester
    } = req.body;
    const existingUser = await User.findOne({ email });
    if (existingUser) throw new ApiError(400, 'User already exists with this email');

    const hashedPassword = await bcrypt.hash(password, 12);

    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000);

    const newUser = await User.create({
        name,
        email,
        password: hashedPassword,
        mobile,
        whatsapp,
        dob,
        collegeName,
        passingYear,
        department,
        preferredLocation,
        semester,
        role: 'student',
        otp: {
            code: otpCode,
            expiresAt: otpExpiry,
            attempts: 0,
            verified: false,
            lastSent: new Date()
        }
    });
    await sendOtpEmail(email, otpCode);

    res.status(201).json(new ApiResponse(201, {
        message: 'Registration successful. Please verify your email using the OTP sent.',
        userId: newUser._id
    }));
});


//!   Login User
exports.login = asyncHandler(async (req, res) => {
    const { email, password } = req.body;
    const user = await User.findOne({ email });

    if (!user || !(await bcrypt.compare(password, user.password))) {
        throw new ApiError(401, 'Invalid credentials');
    }

    if (!user.otp?.verified) {
        throw new ApiError(401, 'Email not verified. Please verify using OTP.');
    }

    const payload = { id: user._id, role: user.role, email: user.email };
    const { accessToken, refreshToken, expiresIn } = generateTokenPair(payload);

    res.cookie('accessToken', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 15 * 60 * 1000
    });
    res.cookie('refreshToken', refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 7 * 24 * 60 * 60 * 1000
    });

    res.status(200).json(new ApiResponse(200, {
        user: {
            id: user._id,
            name: `${user.name.first} ${user.name.last}`,
            email: user.email,
            role: user.role
        },
        accessToken,
        refreshToken,
        expiresIn
    }, 'Login successful'));
});

//!    Logout User
exports.logout = asyncHandler(async (req, res) => {
    const token = req.cookies.accessToken;
    if (token) blacklistToken(token);

    res.clearCookie('accessToken');
    res.clearCookie('refreshToken');
    res.status(200).json(new ApiResponse(200, {}, 'Logged out successfully'));
});

//!    Refresh Token
exports.refreshToken = asyncHandler(async (req, res) => {
    const { refreshToken } = req.cookies;
    if (!refreshToken) throw new ApiError(401, 'No refresh token found');

    const decoded = verifyRefreshToken(refreshToken);
    const user = await User.findById(decoded.id);
    if (!user) throw new ApiError(401, 'User not found');

    const payload = { id: user._id, role: user.role, email: user.email };
    const { accessToken, refreshToken: newRefreshToken, expiresIn } = generateTokenPair(payload);

    res.cookie('accessToken', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 15 * 60 * 1000
    });
    res.cookie('refreshToken', newRefreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 7 * 24 * 60 * 60 * 1000
    });

    res.status(200).json(new ApiResponse(200, {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn
    }, 'Token refreshed'));
});

// *   Resend OTP
exports.resendOTP = asyncHandler(async (req, res) => {
    const { email } = req.body;
    const user = await User.findOne({ email });
    if (!user) throw new ApiError(404, 'User not found');
    if (user.otp?.verified) throw new ApiError(400, 'Email already verified');

    const now = Date.now();

    //  Wait 1 minute between resends
    if (user.otp?.lastSent && now - user.otp.lastSent.getTime() < 60 * 1000) {
        throw new ApiError(429, 'Wait 1 minute before requesting a new OTP');
    }

    //  Reset resend count every 24 hours
    if (!user.otp.resendResetTime || now > user.otp.resendResetTime.getTime()) {
        user.otp.resendCount = 0;
        user.otp.resendResetTime = new Date(now + 24 * 60 * 60 * 1000);
    }

    // ! Max 5 resends per 24 hours
    if (user.otp.resendCount >= 5) {
        throw new ApiError(429, 'OTP resend limit reached for today. Try again later.');
    }
    const newOTP = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = new Date(now + 10 * 60 * 1000);

    user.otp.code = newOTP;
    user.otp.expiresAt = otpExpiry;
    user.otp.lastSent = new Date();
    user.otp.verified = false;
    user.otp.attempts = 0;
    user.otp.resendCount += 1;

    await user.save();
    await sendOtpEmail(email, newOTP);


    res.status(200).json(new ApiResponse(200, {}, 'OTP resent successfully'));
});


// *   Verify OTP
exports.verifyOTP = asyncHandler(async (req, res) => {
    const { email, code } = req.body;
    const user = await User.findOne({ email });
    if (!user || !user.otp) throw new ApiError(400, 'OTP not found or user not registered');

    if (user.otp.verified) {
        return res.status(200).json(new ApiResponse(200, {}, 'Email already verified'));
    }

    if (user.otp.expiresAt < Date.now()) {
        throw new ApiError(400, 'OTP expired');
    }

    if (user.otp.attempts >= 5) {
        throw new ApiError(429, 'Too many attempts. Please resend OTP.');
    }

    if (user.otp.code !== code) {
        user.otp.attempts += 1;
        await user.save();
        throw new ApiError(400, 'Invalid OTP');
    }

    // ✅ Success — verify and clear sensitive data
    user.otp.verified = true;
    user.otp.attempts = 0;
    user.otp.code = null; // Clear code after successful verification
    await user.save();

    res.status(200).json(new ApiResponse(200, {}, 'Email verified successfully'));
});


// *   Get User Profile
exports.getMe = asyncHandler(async (req, res) => {
    const user = await User.findById(req.user.id).select('-password');
    if (!user) throw new ApiError(404, 'User not found');
    res.status(200).json(new ApiResponse(200, user));
});
