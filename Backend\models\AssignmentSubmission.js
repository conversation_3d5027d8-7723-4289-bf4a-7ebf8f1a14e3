const mongoose = require('mongoose');

const assignmentSubmissionSchema = new mongoose.Schema({
    assignment: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Assignment',
        required: true
    },
    student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    fileUrl: {
        type: String,
        required: true
    },
    fileName: {
        type: String,
        required: true
    },
    fileSize: {
        type: Number
    },
    submittedAt: {
        type: Date,
        default: Date.now
    },
    comments: {
        type: String,
        trim: true
    },
    marks: {
        type: Number,
        min: 0
    },
    feedback: {
        type: String,
        trim: true
    },
    isGraded: {
        type: Boolean,
        default: false
    },
    gradedAt: {
        type: Date
    },
    gradedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    isLate: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true
});
assignmentSubmissionSchema.index({ assignment: 1, student: 1 }, { unique: true });
assignmentSubmissionSchema.index({ assignment: 1, isGraded: 1 });

module.exports = mongoose.model('AssignmentSubmission', assignmentSubmissionSchema);
