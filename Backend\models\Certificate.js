const mongoose = require('mongoose');

const certificateSchema = new mongoose.Schema({
    certificateId: {
        type: String,
        required: true,
        unique: true
    },
    student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    batch: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch',
        required: true
    },
    course: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    issueDate: {
        type: Date,
        default: Date.now
    },
    completionDate: {
        type: Date,
        required: true
    },
    grade: {
        type: String,
        trim: true
    },
    percentage: {
        type: Number,
        min: 0,
        max: 100
    },
    certificateUrl: {
        type: String // URL to the generated certificate PDF
    },
    publicId: {
        type: String // Cloudinary public ID for certificate management
    },
    status: {
        type: String,
        enum: ['requested', 'processing', 'issued', 'revoked'],
        default: 'requested'
    },
    issuedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    verificationCode: {
        type: String,
        unique: true
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

// Index for efficient queries
certificateSchema.index({ student: 1, batch: 1 });
certificateSchema.index({ certificateId: 1 });
certificateSchema.index({ verificationCode: 1 });

module.exports = mongoose.model('Certificate', certificateSchema);
