const ApiError = require('../utils/apiError');
const roleMiddleware = (allowedRoles = []) => {
    return (req, res, next) => {
        if (!req.user || !req.user.role) {
            return next(new ApiError(401, 'Unauthorized - No user role found'));
        }

        const userRole = req.user.role;

        const rolesArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

        if (!rolesArray.includes(userRole)) {
            return next(new ApiError(403, 'Forbidden - You do not have permission to access this resource'));
        }

        next();
    };
};

module.exports = roleMiddleware;
