const User = require('../models/User');
const Batch = require('../models/Batch');
const Certificate = require('../models/Certificate');
const College = require('../models/College');
const generateUserId = async (prefix) => {
    let model;
    let field;

    switch (prefix) {
        case 'STU':
        case 'TRN':
            model = User;
            field = 'userId';
            break;
        case 'COL':
            model = College;
            field = 'collegeId';
            break;
        case 'BAT':
            model = Batch;
            field = 'batchId';
            break;
        case 'CRT':
            model = Certificate;
            field = 'certificateId';
            break;
        default:
            throw new Error('Invalid prefix');
    }

    const regex = new RegExp(`^${prefix}`, 'i');

    const latestRecord = await model
        .findOne({ [field]: regex }) // ✅ Avoids casting issues
        .sort({ createdAt: -1 })
        .lean();

    let newNumber = 1;

    if (latestRecord && latestRecord[field]) {
        const match = latestRecord[field].match(/\d+$/);
        if (match) {
            newNumber = parseInt(match[0], 10) + 1;
        }
    }

    return `${prefix}${String(newNumber).padStart(4, '0')}`;
};




module.exports = {
    generateUserId,
};
