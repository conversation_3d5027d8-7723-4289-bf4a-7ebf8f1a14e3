
const {
    uploadMiddleware: cloudinaryUploadMiddleware,
    fileSizeLimits,
    uploadToCloudinary,
    deleteFromCloudinary,
    generateSignedUrl,
    getFileInfo,
    testConnection,
    // csv: createCsvUpload()
} = require('../config/cloudiinary'); // adjust path as needed

module.exports = {
    uploadMiddleware: cloudinaryUploadMiddleware, // Main upload middleware
    uploadToCloudinary,
    deleteFromCloudinary,
    generateSignedUrl,
    getFileInfo,
    testConnection,
    fileSizeLimits
};
