const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { jwtConfig,
    generateAccessToken,
    generateRefreshToken,
    generateTokenPair,
    verifyAccessToken,
    verifyRefreshToken,
    decodeToken,
    getTokenExpiration,
    isTokenExpired,
    generatePasswordResetToken,
    verifyPasswordResetToken,
    generateEmailVerificationToken,
    verifyEmailVerificationToken,
    extractTokenFromHeader,
    blacklistToken,
    isTokenBlacklisted,
    clearExpiredBlacklistedTokens } = require('../config/jwt');

const protect = async (req, res, next) => {
    try {
        let token;

        if (req.cookies && req.cookies.accessToken) {
            token = req.cookies.accessToken;
        }
        else if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
        }

        //  No token provided
        if (!token) {
            return res.status(401).json({
                success: false,
                error: 'Not authorized to access this route (token missing)'
            });
        }

        //  Verify token
        const decoded = jwt.verify(token, jwtConfig.accessTokenSecret);

        const user = await User.findById(decoded.id);

        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'User not found'
            });
        }

        req.user = user;
        next();
    } catch (err) {
        return res.status(401).json({
            success: false,
            error: 'Not authorized to access this route (token invalid)'
        });
    }
};

const authorize = (...roles) => {
    return (req, res, next) => {
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                error: `User role ${req.user.role} is not authorized to access this route`
            });
        }
        next();
    };
};

module.exports = { protect, authorize };
