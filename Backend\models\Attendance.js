const mongoose = require('mongoose');

const attendanceSchema = new mongoose.Schema({
    studentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    batchId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch',
        required: true,
    },
    date: {
        type: Date,
        required: true,
    },
    status: {
        type: Boolean, // true = Present, false = Absent
        required: true,
    },
    uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Trainer who uploaded the attendance
        required: true,
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Attendance', attendanceSchema);
