// server.js
const app = require('./app');
// const connectDB = require('./config/dbConfig');
//!MOre secure connection
const {connectDB} = require('./config/db');

const dotenv = require('dotenv');
const logger = require('./utils/logger');
require('./cron/deleteUnverifiedUsers'); // Auto-starts cron

// Load environment variables
dotenv.config();

const PORT = process.env.PORT || 5000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Graceful shutdown handler
const gracefulShutdown = (server) => {
    return (signal) => {
        logger.info(`Received ${signal}. Starting graceful shutdown...`);

        server.close(() => {
            logger.info('HTTP server closed');
            process.exit(0);
        });

        // Force close after 10 seconds
        setTimeout(() => {
            logger.error('Could not close connections in time, forcefully shutting down');
            process.exit(1);
        }, 10000);
    };
};

// Start server
const startServer = async () => {
    try {
        // Connect to database
        await connectDB();
        logger.info('Database connected successfully');

        // Start HTTP server
        const server = app.listen(PORT, () => {
            logger.info(`Server running on http://localhost:${PORT}`);
            logger.info(`Environment: ${NODE_ENV}`);
        });

        // Handle graceful shutdown
        process.on('SIGTERM', gracefulShutdown(server));
        process.on('SIGINT', gracefulShutdown(server));

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (err) => {
            logger.error('Unhandled Promise Rejection:', err);
            server.close(() => {
                process.exit(1);
            });
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (err) => {
            logger.error('Uncaught Exception:', err);
            process.exit(1);
        });

    } catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
};

// Start the application
startServer();