
const asyncHandler = require('../utils/asyncHandler');
const ApiError = require('../utils/ApiError.js');
const ApiResponse = require('../utils/ApiResponse.js');
const User = require('../models/User');
const Batch = require('../models/Batch');
const Course = require('../models/Course');
const Assignment = require('../models/Assignment');
const AssignmentSubmission = require('../models/AssignmentSubmission');
const Attendance = require('../models/Attendance');
const Assessment = require('../models/Assessment');
const CourseMaterial = require('../models/CourseMaterial');
const Notification = require('../models/Notification');
const Feedback = require('../models/Feedback');
const Certificate = require('../models/Certificate');
const { cloudinaryBufferUpload } = require('../middlewares/uploadMiddleware.js');
const mongoose = require('mongoose');

// ================================
// DASHBOARD
// ================================
const getDashboard = asyncHandler(async (req, res) => {
    const studentId = req.user._id;

    // Get enrolled batches
    const enrolledBatches = await Batch.find({ students: studentId })
        .populate('courseId', 'title description')
        .populate('trainerId', 'firstName lastName')
        .select('batchId courseName startDate endDate status');

    // Get recent assignments
    const recentAssignments = await Assignment.find({
        batch: { $in: enrolledBatches.map(b => b._id) }
    })
        .populate('batch', 'batchId courseName')
        .sort({ createdAt: -1 })
        .limit(5);

    // Get pending submissions
    const pendingAssignments = await Assignment.find({
        batch: { $in: enrolledBatches.map(b => b._id) },
        dueDate: { $gte: new Date() },
        _id: {
            $nin: await AssignmentSubmission.distinct('assignment', { student: studentId })
        }
    }).countDocuments();

    // Get recent notifications
    const recentNotifications = await Notification.find({
        recipients: studentId,
        readBy: { $ne: studentId }
    })
        .populate('batch', 'batchId courseName')
        .sort({ createdAt: -1 })
        .limit(5);

    // Get overall attendance percentage
    const totalClasses = await Attendance.countDocuments({
        studentId: studentId
    });
    const attendedClasses = await Attendance.countDocuments({
        studentId: studentId,
        status: true
    });
    const attendancePercentage = totalClasses > 0 ? Math.round((attendedClasses / totalClasses) * 100) : 0;

    // Get recent assessment results
    const recentResults = await Assessment.find({
        batch: { $in: enrolledBatches.map(b => b._id) },
        'studentMarks.student': studentId
    })
        .sort({ createdAt: -1 })
        .limit(3)
        .then(assessments =>
            assessments.map(assessment => {
                const studentMark = assessment.studentMarks.find(
                    mark => mark.student.toString() === studentId.toString()
                );
                return {
                    _id: assessment._id,
                    title: assessment.title,
                    type: assessment.type,
                    marks: studentMark.marks,
                    maxMarks: assessment.maxMarks,
                    percentage: Math.round((studentMark.marks / assessment.maxMarks) * 100),
                    date: assessment.date
                };
            })
        );

    const dashboardData = {
        stats: {
            enrolledBatches: enrolledBatches.length,
            activeBatches: enrolledBatches.filter(batch => batch.status === 'ongoing').length,
            pendingAssignments,
            unreadNotifications: recentNotifications.length,
            attendancePercentage
        },
        enrolledBatches: enrolledBatches.map(batch => ({
            _id: batch._id,
            batchId: batch.batchId,
            courseName: batch.courseName,
            startDate: batch.startDate,
            endDate: batch.endDate,
            status: batch.status,
            trainer: batch.trainerId,
            course: batch.courseId
        })),
        recentAssignments,
        recentNotifications,
        recentResults
    };

    res.status(200).json(new ApiResponse(200, dashboardData, 'Dashboard data retrieved successfully'));
});

// ================================
// PROFILE MANAGEMENT
// ================================
const updateProfile = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const updateData = req.body;

    // Remove sensitive fields that shouldn't be updated via this endpoint
    delete updateData.password;
    delete updateData.role;
    delete updateData.userId;
    delete updateData.batchId;

    const student = await User.findByIdAndUpdate(
        studentId,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
    ).select('-password');

    if (!student) {
        throw new ApiError(404, 'Student not found');
    }

    res.status(200).json(new ApiResponse(200, student, 'Profile updated successfully'));
});

// ================================
// REPORTS & ANALYTICS
// ================================
const getPerformanceReport = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { batchId, period = '6months' } = req.query;

    let startDate;
    const endDate = new Date();

    switch (period) {
        case '1month':
            startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            break;
        case '3months':
            startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
            break;
        case '6months':
            startDate = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
            break;
        case '1year':
            startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
            break;
        default:
            startDate = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
    }

    // Get enrolled batches
    const batchFilter = { students: studentId };
    if (batchId) batchFilter._id = batchId;

    const batches = await Batch.find(batchFilter)
        .populate('courseId', 'title')
        .populate('trainerId', 'firstName lastName');

    const performanceData = await Promise.all(
        batches.map(async (batch) => {
            // Assessment performance
            const assessments = await Assessment.find({
                batch: batch._id,
                'studentMarks.student': studentId,
                date: { $gte: startDate, $lte: endDate }
            }).sort({ date: 1 });

            const assessmentScores = assessments.map(assessment => {
                const myMark = assessment.studentMarks.find(
                    mark => mark.student.toString() === studentId.toString()
                );
                return {
                    title: assessment.title,
                    type: assessment.type,
                    date: assessment.date,
                    percentage: myMark ? Math.round((myMark.marks / assessment.maxMarks) * 100) : 0,
                    marks: myMark ? myMark.marks : 0,
                    maxMarks: assessment.maxMarks
                };
            });
            // Assignment performance
            const assignments = await Assignment.find({
                batch: batch._id,
                createdAt: { $gte: startDate, $lte: endDate }
            });
            const assignmentPerformance = await Promise.all(
                assignments.map(async (assignment) => {
                    const submission = await AssignmentSubmission.findOne({
                        assignment: assignment._id,
                        student: studentId
                    });
                    return {
                        title: assignment.title,
                        dueDate: assignment.dueDate,
                        maxMarks: assignment.maxMarks,
                        submitted: !!submission,
                        submittedOn: submission ? submission.submittedAt : null,
                        marks: submission ? submission.marks : null,
                        percentage: submission && submission.marks ?
                            Math.round((submission.marks / assignment.maxMarks) * 100) : null,
                        isLate: submission ? submission.submittedAt > assignment.dueDate : false
                    };
                })
            );
            // Attendance performance
            const attendanceData = await Attendance.find({
                batchId: batch._id,
                studentId: studentId,
                date: { $gte: startDate, $lte: endDate }
            }).sort({ date: 1 });
            const monthlyAttendance = attendanceData.reduce((acc, record) => {
                const monthKey = record.date.toISOString().substring(0, 7); // YYYY-MM
                if (!acc[monthKey]) {
                    acc[monthKey] = { total: 0, present: 0 };
                }
                acc[monthKey].total++;
                if (record.status) acc[monthKey].present++;
                return acc;
            }, {});
            // Calculate trends
            const assessmentTrend = assessmentScores.length >= 2 ?
                assessmentScores[assessmentScores.length - 1].percentage - assessmentScores[0].percentage : 0;
            const totalAssignments = assignmentPerformance.length;
            const submittedAssignments = assignmentPerformance.filter(a => a.submitted).length;
            const averageAssignmentScore = assignmentPerformance
                .filter(a => a.percentage !== null)
                .reduce((sum, a, _, arr) => sum + a.percentage / arr.length, 0);
            const overallAttendance = attendanceData.length > 0 ?
                Math.round((attendanceData.filter(a => a.status).length / attendanceData.length) * 100) : 0;
            return {
                batch: {
                    _id: batch._id,
                    batchId: batch.batchId,
                    courseName: batch.courseName,
                    course: batch.courseId,
                    trainer: batch.trainerId
                },
                performance: {
                    assessments: {
                        total: assessmentScores.length,
                        averageScore: assessmentScores.length > 0 ?
                            Math.round(assessmentScores.reduce((sum, a) => sum + a.percentage, 0) / assessmentScores.length) : 0,
                        trend: Math.round(assessmentTrend),
                        scores: assessmentScores
                    },
                    assignments: {
                        total: totalAssignments,
                        submitted: submittedAssignments,
                        submissionRate: totalAssignments > 0 ? Math.round((submittedAssignments / totalAssignments) * 100) : 0,
                        averageScore: Math.round(averageAssignmentScore) || 0,
                        performance: assignmentPerformance
                    },
                    attendance: {
                        overall: overallAttendance,
                        totalClasses: attendanceData.length,
                        attendedClasses: attendanceData.filter(a => a.status).length,
                        monthlyBreakdown: Object.keys(monthlyAttendance).map(month => ({
                            month,
                            percentage: Math.round((monthlyAttendance[month].present / monthlyAttendance[month].total) * 100),
                            present: monthlyAttendance[month].present,
                            total: monthlyAttendance[month].total
                        }))
                    }
                }
            };
        })
    );

    res.status(200).json(new ApiResponse(200, {
        period,
        batches: performanceData
    }, 'Performance report generated successfully'));
});

const getAttendanceReport = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { batchId, startDate, endDate } = req.query;

    // Date range setup
    let dateFilter = {};
    if (startDate && endDate) {
        dateFilter.date = {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
        };
    } else {
        // Default to last 3 months
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
        dateFilter.date = { $gte: threeMonthsAgo };
    }

    // Batch filter
    let batchIds = [];
    if (batchId) {
        // Verify student is enrolled
        const batch = await Batch.findOne({ _id: batchId, students: studentId });
        if (!batch) {
            throw new ApiError(404, 'Batch not found or you are not enrolled');
        }
        batchIds = [new mongoose.Types.ObjectId(batchId)];
    } else {
        // Get all enrolled batches
        const enrolledBatches = await Batch.find({ students: studentId });
        batchIds = enrolledBatches.map(b => b._id);
    }

    // Get attendance data
    const attendanceFilter = {
        studentId: studentId,
        batchId: { $in: batchIds },
        ...dateFilter
    };

    const attendanceRecords = await Attendance.find(attendanceFilter)
        .populate('batchId', 'batchId courseName')
        .sort({ date: -1 });

    // Group by batch
    const batchWiseAttendance = attendanceRecords.reduce((acc, record) => {
        const batchKey = record.batchId._id.toString();
        if (!acc[batchKey]) {
            acc[batchKey] = {
                batch: record.batchId,
                records: [],
                stats: { total: 0, present: 0 }
            };
        }
        acc[batchKey].records.push(record);
        acc[batchKey].stats.total++;
        if (record.status) acc[batchKey].stats.present++;
        return acc;
    }, {});

    // Calculate statistics for each batch
    const batchReports = Object.values(batchWiseAttendance).map(batchData => ({
        batch: batchData.batch,
        totalClasses: batchData.stats.total,
        attendedClasses: batchData.stats.present,
        absentClasses: batchData.stats.total - batchData.stats.present,
        attendancePercentage: batchData.stats.total > 0 ?
            Math.round((batchData.stats.present / batchData.stats.total) * 100) : 0,
        records: batchData.records
    }));

    // Overall statistics
    const overallStats = {
        totalClasses: attendanceRecords.length,
        attendedClasses: attendanceRecords.filter(r => r.status).length,
        absentClasses: attendanceRecords.filter(r => !r.status).length,
        overallPercentage: attendanceRecords.length > 0 ?
            Math.round((attendanceRecords.filter(r => r.status).length / attendanceRecords.length) * 100) : 0
    };

    // Monthly trend
    const monthlyTrend = attendanceRecords.reduce((acc, record) => {
        const monthKey = record.date.toISOString().substring(0, 7); // YYYY-MM
        if (!acc[monthKey]) {
            acc[monthKey] = { total: 0, present: 0 };
        }
        acc[monthKey].total++;
        if (record.status) acc[monthKey].present++;
        return acc;
    }, {});

    const monthlyTrendArray = Object.keys(monthlyTrend).sort().map(month => ({
        month,
        totalClasses: monthlyTrend[month].total,
        attendedClasses: monthlyTrend[month].present,
        percentage: Math.round((monthlyTrend[month].present / monthlyTrend[month].total) * 100)
    }));

    res.status(200).json(new ApiResponse(200, {
        overall: overallStats,
        batchWise: batchReports,
        monthlyTrend: monthlyTrendArray,
        period: {
            startDate: dateFilter.date?.$gte || 'N/A',
            endDate: dateFilter.date?.$lte || new Date()
        }
    }, 'Attendance report generated successfully'));
});

// ================================
// BATCH & COURSE INFORMATION
// ================================
const getEnrolledBatches = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { status, page = 1, limit = 10 } = req.query;

    const filter = { students: studentId };
    if (status) filter.status = status;

    const batches = await Batch.find(filter)
        .populate('courseId', 'title description duration')
        .populate('trainerId', 'firstName lastName email')
        .populate('collegeId', 'collegeName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Batch.countDocuments(filter);

    // Add progress information for each batch
    const batchesWithProgress = await Promise.all(
        batches.map(async (batch) => {
            const totalMaterials = await CourseMaterial.countDocuments({ batch: batch._id });
            const totalAssignments = await Assignment.countDocuments({ batch: batch._id });
            const submittedAssignments = await AssignmentSubmission.countDocuments({
                student: studentId,
                assignment: { $in: await Assignment.distinct('_id', { batch: batch._id }) }
            });

            return {
                ...batch.toObject(),
                progress: {
                    totalMaterials,
                    totalAssignments,
                    submittedAssignments,
                    assignmentProgress: totalAssignments > 0 ?
                        Math.round((submittedAssignments / totalAssignments) * 100) : 0
                }
            };
        })
    );

    res.status(200).json(new ApiResponse(200, {
        batches: batchesWithProgress,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total,
            hasNext: page < Math.ceil(total / limit),
            hasPrev: page > 1
        }
    }, 'Enrolled batches retrieved successfully'));
});

const getBatchDetails = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const studentId = req.user._id;

    const batch = await Batch.findOne({ _id: batchId, students: studentId })
        .populate('courseId', 'title description duration syllabus')
        .populate('trainerId', 'firstName lastName email')
        .populate('collegeId', 'collegeName address')
        .populate('students', 'firstName lastName', null, { limit: 10 }); // Limit student details

    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not enrolled');
    }

    // Get batch statistics relevant to student
    const totalMaterials = await CourseMaterial.countDocuments({ batch: batchId });
    const totalAssignments = await Assignment.countDocuments({ batch: batchId });
    const mySubmissions = await AssignmentSubmission.countDocuments({
        student: studentId,
        assignment: { $in: await Assignment.distinct('_id', { batch: batchId }) }
    });
    const totalAssessments = await Assessment.countDocuments({ batch: batchId });

    // My attendance in this batch
    const myAttendance = await Attendance.aggregate([
        { $match: { batchId: new mongoose.Types.ObjectId(batchId), studentId: studentId } },
        {
            $group: {
                _id: null,
                totalClasses: { $sum: 1 },
                attendedClasses: { $sum: { $cond: ['$status', 1, 0] } }
            }
        }
    ]);

    const attendanceStats = myAttendance.length > 0 ? myAttendance[0] : { totalClasses: 0, attendedClasses: 0 };

    const batchDetails = {
        ...batch.toObject(),
        myStats: {
            totalMaterials,
            totalAssignments,
            submittedAssignments: mySubmissions,
            totalAssessments,
            attendancePercentage: attendanceStats.totalClasses > 0 ?
                Math.round((attendanceStats.attendedClasses / attendanceStats.totalClasses) * 100) : 0,
            totalClasses: attendanceStats.totalClasses,
            attendedClasses: attendanceStats.attendedClasses
        }
    };

    res.status(200).json(new ApiResponse(200, batchDetails, 'Batch details retrieved successfully'));
});

const getCourseDetails = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const studentId = req.user._id;

    // Verify student is enrolled in this batch
    const batch = await Batch.findOne({ _id: batchId, students: studentId })
        .populate('courseId');

    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not enrolled');
    }

    const course = batch.courseId;
    if (!course) {
        throw new ApiError(404, 'Course details not found');
    }

    // Get course materials count
    const materialsCount = await CourseMaterial.countDocuments({ batch: batchId });
    const assignmentsCount = await Assignment.countDocuments({ batch: batchId });

    const courseDetails = {
        ...course.toObject(),
        batch: {
            batchId: batch.batchId,
            startDate: batch.startDate,
            endDate: batch.endDate,
            status: batch.status
        },
        statistics: {
            materialsCount,
            assignmentsCount
        }
    };

    res.status(200).json(new ApiResponse(200, courseDetails, 'Course details retrieved successfully'));
});

// ================================
// COURSE MATERIALS
// ================================
const getBatchMaterials = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const studentId = req.user._id;
    const { type, page = 1, limit = 10 } = req.query;

    // Verify student is enrolled in this batch
    const batch = await Batch.findOne({ _id: batchId, students: studentId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not enrolled');
    }

    const filter = { batch: batchId };
    if (type) filter.type = type;

    const materials = await CourseMaterial.find(filter)
        .populate('uploadedBy', 'firstName lastName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await CourseMaterial.countDocuments(filter);

    res.status(200).json(new ApiResponse(200, {
        materials,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Course materials retrieved successfully'));
});

const getMaterialDetails = asyncHandler(async (req, res) => {
    const { materialId } = req.params;
    const studentId = req.user._id;

    const material = await CourseMaterial.findById(materialId)
        .populate('uploadedBy', 'firstName lastName')
        .populate('batch', 'batchId courseName students');

    if (!material) {
        throw new ApiError(404, 'Material not found');
    }

    // Check if student is enrolled in the batch
    if (!material.batch.students.includes(studentId)) {
        throw new ApiError(403, 'Access denied to this material');
    }

    res.status(200).json(new ApiResponse(200, material, 'Material details retrieved successfully'));
});

const downloadMaterial = asyncHandler(async (req, res) => {
    const { materialId } = req.params;
    const studentId = req.user._id;

    const material = await CourseMaterial.findById(materialId)
        .populate('batch', 'students');

    if (!material) {
        throw new ApiError(404, 'Material not found');
    }

    // Check if student is enrolled in the batch
    if (!material.batch.students.includes(studentId)) {
        throw new ApiError(403, 'Access denied to download this material');
    }

    // Increment download count
    material.downloadCount = (material.downloadCount || 0) + 1;
    await material.save();

    // Return download URL (for Cloudinary, this would be the secure_url)
    res.status(200).json(new ApiResponse(200, {
        downloadUrl: material.fileUrl,
        fileName: material.fileName
    }, 'Material download link generated successfully'));
});

// ================================
// ASSIGNMENTS
// ================================
const getBatchAssignments = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const studentId = req.user._id;
    const { status, page = 1, limit = 10 } = req.query;

    // Verify student is enrolled in this batch
    const batch = await Batch.findOne({ _id: batchId, students: studentId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not enrolled');
    }

    const filter = { batch: batchId };

    const assignments = await Assignment.find(filter)
        .populate('uploadedBy', 'firstName lastName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    // Get submission status for each assignment
    const assignmentsWithStatus = await Promise.all(
        assignments.map(async (assignment) => {
            const submission = await AssignmentSubmission.findOne({
                assignment: assignment._id,
                student: studentId
            });

            let submissionStatus = 'not_submitted';
            let isOverdue = false;

            if (submission) {
                submissionStatus = submission.isGraded ? 'graded' : 'submitted';
            } else if (new Date() > assignment.dueDate) {
                isOverdue = true;
                submissionStatus = 'overdue';
            }

            return {
                ...assignment.toObject(),
                submissionStatus,
                isOverdue,
                mySubmission: submission ? {
                    _id: submission._id,
                    submittedAt: submission.submittedAt,
                    marks: submission.marks,
                    feedback: submission.feedback,
                    isGraded: submission.isGraded
                } : null
            };
        })
    );

    // Filter by status if requested
    let filteredAssignments = assignmentsWithStatus;
    if (status) {
        filteredAssignments = assignmentsWithStatus.filter(assignment =>
            assignment.submissionStatus === status
        );
    }

    const total = await Assignment.countDocuments(filter);

    res.status(200).json(new ApiResponse(200, {
        assignments: filteredAssignments,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Assignments retrieved successfully'));
});

const getAssignmentDetails = asyncHandler(async (req, res) => {
    const { assignmentId } = req.params;
    const studentId = req.user._id;

    const assignment = await Assignment.findById(assignmentId)
        .populate('uploadedBy', 'firstName lastName')
        .populate('batch', 'batchId courseName students');

    if (!assignment) {
        throw new ApiError(404, 'Assignment not found');
    }

    // Check if student is enrolled in the batch
    if (!assignment.batch.students.includes(studentId)) {
        throw new ApiError(403, 'Access denied to this assignment');
    }

    // Get my submission
    const mySubmission = await AssignmentSubmission.findOne({
        assignment: assignmentId,
        student: studentId
    }).populate('gradedBy', 'firstName lastName');

    const assignmentDetails = {
        ...assignment.toObject(),
        mySubmission,
        isOverdue: new Date() > assignment.dueDate && !mySubmission,
        canSubmit: new Date() <= assignment.dueDate || !mySubmission
    };

    res.status(200).json(new ApiResponse(200, assignmentDetails, 'Assignment details retrieved successfully'));
});

const submitAssignment = asyncHandler(async (req, res) => {
    const { assignmentId } = req.params;
    const { comments } = req.body;
    const studentId = req.user._id;

    const assignment = await Assignment.findById(assignmentId)
        .populate('batch', 'students');

    if (!assignment) {
        throw new ApiError(404, 'Assignment not found');
    }

    // Check if student is enrolled in the batch
    if (!assignment.batch.students.includes(studentId)) {
        throw new ApiError(403, 'Access denied to submit this assignment');
    }

    // Check if already submitted
    const existingSubmission = await AssignmentSubmission.findOne({
        assignment: assignmentId,
        student: studentId
    });

    if (existingSubmission) {
        throw new ApiError(400, 'Assignment already submitted. Use update endpoint to modify.');
    }

    // Check if file is provided
    if (!req.file) {
        throw new ApiError(400, 'Please upload your assignment file');
    }

    // Upload file to Cloudinary
    const uploadResult = await cloudinaryBufferUpload(
        req.file.buffer,
        'college-management/submissions'
    );

    const submission = await AssignmentSubmission.create({
        assignment: assignmentId,
        student: studentId,
        fileUrl: uploadResult.secure_url,
        fileName: req.file.originalname,
        fileSize: req.file.size,
        publicId: uploadResult.public_id,
        comments: comments || '',
        submittedAt: new Date()
    });

    const populatedSubmission = await AssignmentSubmission.findById(submission._id)
        .populate('assignment', 'title maxMarks dueDate')
        .populate('student', 'firstName lastName');

    // Send notification to trainer
    await Notification.create({
        title: 'New Assignment Submission',
        message: `${req.user.firstName} ${req.user.lastName} has submitted "${assignment.title}"`,
        type: 'submission',
        recipients: [assignment.uploadedBy],
        createdBy: studentId,
        relatedId: submission._id
    });

    res.status(201).json(new ApiResponse(201, populatedSubmission, 'Assignment submitted successfully'));
});

const getMySubmission = asyncHandler(async (req, res) => {
    const { assignmentId } = req.params;
    const studentId = req.user._id;

    const submission = await AssignmentSubmission.findOne({
        assignment: assignmentId,
        student: studentId
    })
        .populate('assignment', 'title maxMarks dueDate')
        .populate('gradedBy', 'firstName lastName');

    if (!submission) {
        throw new ApiError(404, 'Submission not found');
    }

    res.status(200).json(new ApiResponse(200, submission, 'Submission retrieved successfully'));
});

const updateSubmission = asyncHandler(async (req, res) => {
    const { assignmentId } = req.params;
    const { comments } = req.body;
    const studentId = req.user._id;

    const submission = await AssignmentSubmission.findOne({
        assignment: assignmentId,
        student: studentId
    });

    if (!submission) {
        throw new ApiError(404, 'Submission not found');
    }

    if (submission.isGraded) {
        throw new ApiError(400, 'Cannot update submission that has already been graded');
    }

    const assignment = await Assignment.findById(assignmentId);
    if (new Date() > assignment.dueDate) {
        throw new ApiError(400, 'Cannot update submission after due date');
    }

    // Update file if provided
    if (req.file) {
        // Delete old file from Cloudinary if exists
        if (submission.publicId) {
            const { deleteFromCloudinary } = require('../middleware/upload');
            await deleteFromCloudinary(submission.publicId);
        }

        // Upload new file
        const uploadResult = await cloudinaryBufferUpload(
            req.file.buffer,
            'college-management/submissions'
        );

        submission.fileUrl = uploadResult.secure_url;
        submission.fileName = req.file.originalname;
        submission.fileSize = req.file.size;
        submission.publicId = uploadResult.public_id;
    }

    // Update comments
    if (comments !== undefined) {
        submission.comments = comments;
    }

    submission.updatedAt = new Date();
    await submission.save();

    const populatedSubmission = await AssignmentSubmission.findById(submission._id)
        .populate('assignment', 'title maxMarks dueDate');

    res.status(200).json(new ApiResponse(200, populatedSubmission, 'Submission updated successfully'));
});

// ================================
// ATTENDANCE
// ================================
const getMyAttendance = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { batchId, startDate, endDate, page = 1, limit = 10 } = req.query;

    const filter = { studentId: studentId };
    if (batchId) filter.batchId = batchId;

    if (startDate && endDate) {
        filter.date = {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
        };
    }

    const attendance = await Attendance.find(filter)
        .populate('batchId', 'batchId courseName')
        .sort({ date: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Attendance.countDocuments(filter);

    // Calculate summary statistics
    const summary = await Attendance.aggregate([
        { $match: filter },
        {
            $group: {
                _id: null,
                totalClasses: { $sum: 1 },
                attendedClasses: { $sum: { $cond: ['$status', 1, 0] } }
            }
        }
    ]);

    const stats = summary.length > 0 ? summary[0] : { totalClasses: 0, attendedClasses: 0 };
    const attendancePercentage = stats.totalClasses > 0 ?
        Math.round((stats.attendedClasses / stats.totalClasses) * 100) : 0;

    res.status(200).json(new ApiResponse(200, {
        attendance,
        summary: {
            totalClasses: stats.totalClasses,
            attendedClasses: stats.attendedClasses,
            absentClasses: stats.totalClasses - stats.attendedClasses,
            attendancePercentage
        },
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Attendance records retrieved successfully'));
});

const getBatchAttendance = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const studentId = req.user._id;
    const { month, year } = req.query;

    // Verify student is enrolled in this batch
    const batch = await Batch.findOne({ _id: batchId, students: studentId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not enrolled');
    }

    const filter = { batchId: batchId, studentId: studentId };

    if (month && year) {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0, 23, 59, 59, 999);
        filter.date = { $gte: startDate, $lte: endDate };
    }

    const attendance = await Attendance.find(filter)
        .sort({ date: -1 });

    // Group by date for calendar view
    const attendanceByDate = attendance.reduce((acc, record) => {
        const dateKey = record.date.toISOString().split('T')[0];
        acc[dateKey] = record.status;
        return acc;
    }, {});

    // Calculate monthly summary
    const totalClasses = attendance.length;
    const attendedClasses = attendance.filter(record => record.status).length;
    const attendancePercentage = totalClasses > 0 ?
        Math.round((attendedClasses / totalClasses) * 100) : 0;

    res.status(200).json(new ApiResponse(200, {
        attendance,
        attendanceByDate,
        summary: {
            totalClasses,
            attendedClasses,
            absentClasses: totalClasses - attendedClasses,
            attendancePercentage
        }
    }, 'Batch attendance retrieved successfully'));
});

const getAttendanceSummary = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { period = '6months' } = req.query;

    let startDate;
    const endDate = new Date();

    switch (period) {
        case '1month':
            startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            break;
        case '3months':
            startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
            break;
        case '6months':
            startDate = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
            break;
        case '1year':
            startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
            break;
        default:
            startDate = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
    }

    // Overall summary
    const overallSummary = await Attendance.aggregate([
        {
            $match: {
                studentId: studentId,
                date: { $gte: startDate, $lte: endDate }
            }
        },
        {
            $group: {
                _id: null,
                totalClasses: { $sum: 1 },
                attendedClasses: { $sum: { $cond: ['$status', 1, 0] } }
            }
        }
    ]);

    // Batch-wise summary
    const batchWiseSummary = await Attendance.aggregate([
        {
            $match: {
                studentId: studentId,
                date: { $gte: startDate, $lte: endDate }
            }
        },
        {
            $lookup: {
                from: 'batches',
                localField: 'batchId',
                foreignField: '_id',
                as: 'batch'
            }
        },
        { $unwind: '$batch' },
        {
            $group: {
                _id: '$batchId',
                batchName: { $first: '$batch.batchId' },
                courseName: { $first: '$batch.courseName' },
                totalClasses: { $sum: 1 },
                attendedClasses: { $sum: { $cond: ['$status', 1, 0] } }
            }
        },
        {
            $project: {
                batchName: 1,
                courseName: 1,
                totalClasses: 1,
                attendedClasses: 1,
                attendancePercentage: {
                    $round: [{ $multiply: [{ $divide: ['$attendedClasses', '$totalClasses'] }, 100] }, 0]
                }
            }
        }
    ]);

    // Monthly trend
    const monthlyTrend = await Attendance.aggregate([
        {
            $match: {
                studentId: studentId,
                date: { $gte: startDate, $lte: endDate }
            }
        },
        {
            $group: {
                _id: {
                    year: { $year: '$date' },
                    month: { $month: '$date' }
                },
                totalClasses: { $sum: 1 },
                attendedClasses: { $sum: { $cond: ['$status', 1, 0] } }
            }
        },
        {
            $project: {
                month: '$_id.month',
                year: '$_id.year',
                totalClasses: 1,
                attendedClasses: 1,
                attendancePercentage: {
                    $round: [{ $multiply: [{ $divide: ['$attendedClasses', '$totalClasses'] }, 100] }, 0]
                }
            }
        },
        { $sort: { year: 1, month: 1 } }
    ]);

    const overall = overallSummary.length > 0 ? overallSummary[0] : { totalClasses: 0, attendedClasses: 0 };
    const overallPercentage = overall.totalClasses > 0 ?
        Math.round((overall.attendedClasses / overall.totalClasses) * 100) : 0;

    res.status(200).json(new ApiResponse(200, {
        overall: {
            totalClasses: overall.totalClasses,
            attendedClasses: overall.attendedClasses,
            absentClasses: overall.totalClasses - overall.attendedClasses,
            attendancePercentage: overallPercentage
        },
        batchWise: batchWiseSummary,
        monthlyTrend,
        period
    }, 'Attendance summary retrieved successfully'));
});

// ================================
// MARKS & RESULTS
// ================================
const getMyResults = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { batchId, page = 1, limit = 10 } = req.query;

    // Get enrolled batches
    const enrolledBatches = await Batch.find({ students: studentId }).distinct('_id');

    const filter = {
        batch: { $in: enrolledBatches },
        'studentMarks.student': studentId
    };

    if (batchId) {
        filter.batch = batchId;
    }

    const assessments = await Assessment.find(filter)
        .populate('batch', 'batchId courseName')
        .populate('uploadedBy', 'firstName lastName')
        .sort({ date: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Assessment.countDocuments(filter);

    // Extract my results from each assessment
    const myResults = assessments.map(assessment => {
        const myMark = assessment.studentMarks.find(
            mark => mark.student.toString() === studentId.toString()
        );

        return {
            _id: assessment._id,
            title: assessment.title,
            type: assessment.type,
            subject: assessment.subject,
            date: assessment.date,
            maxMarks: assessment.maxMarks,
            myMarks: myMark ? myMark.marks : 0,
            percentage: myMark ? Math.round((myMark.marks / assessment.maxMarks) * 100) : 0,
            grade: myMark ? myMark.grade : 'N/A',
            remarks: myMark ? myMark.remarks : '',
            batch: assessment.batch,
            trainer: assessment.uploadedBy
        };
    });

    // Calculate overall statistics
    const totalAssessments = myResults.length;
    const averagePercentage = totalAssessments > 0 ?
        Math.round(myResults.reduce((sum, result) => sum + result.percentage, 0) / totalAssessments) : 0;

    res.status(200).json(new ApiResponse(200, {
        results: myResults,
        summary: {
            totalAssessments,
            averagePercentage,
            highestScore: totalAssessments > 0 ? Math.max(...myResults.map(r => r.percentage)) : 0,
            lowestScore: totalAssessments > 0 ? Math.min(...myResults.map(r => r.percentage)) : 0
        },
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Results retrieved successfully'));
});

const getBatchResults = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const studentId = req.user._id;

    // Verify student is enrolled in this batch
    const batch = await Batch.findOne({ _id: batchId, students: studentId })
        .populate('courseId', 'title')
        .populate('trainerId', 'firstName lastName');

    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not enrolled');
    }

    const assessments = await Assessment.find({
        batch: batchId,
        'studentMarks.student': studentId
    }).sort({ date: -1 });

    const results = assessments.map(assessment => {
        const myMark = assessment.studentMarks.find(
            mark => mark.student.toString() === studentId.toString()
        );

        // Calculate class average for comparison
        const classAverage = assessment.studentMarks.length > 0 ?
            assessment.studentMarks.reduce((sum, mark) => sum + mark.marks, 0) / assessment.studentMarks.length : 0;

        return {
            _id: assessment._id,
            title: assessment.title,
            type: assessment.type,
            subject: assessment.subject,
            date: assessment.date,
            maxMarks: assessment.maxMarks,
            myMarks: myMark ? myMark.marks : 0,
            percentage: myMark ? Math.round((myMark.marks / assessment.maxMarks) * 100) : 0,
            grade: myMark ? myMark.grade : 'N/A',
            remarks: myMark ? myMark.remarks : '',
            classAverage: Math.round(classAverage * 100) / 100,
            classAveragePercentage: Math.round((classAverage / assessment.maxMarks) * 100)
        };
    });

    // Calculate batch-wise statistics
    const totalAssessments = results.length;
    const myAveragePercentage = totalAssessments > 0 ?
        Math.round(results.reduce((sum, result) => sum + result.percentage, 0) / totalAssessments) : 0;

    res.status(200).json(new ApiResponse(200, {
        batch: {
            _id: batch._id,
            batchId: batch.batchId,
            course: batch.courseId,
            trainer: batch.trainerId
        },
        results,
        summary: {
            totalAssessments,
            myAveragePercentage,
            highestScore: totalAssessments > 0 ? Math.max(...results.map(r => r.percentage)) : 0,
            lowestScore: totalAssessments > 0 ? Math.min(...results.map(r => r.percentage)) : 0
        }
    }, 'Batch results retrieved successfully'));
});

const getAssessmentResult = asyncHandler(async (req, res) => {
    const { assessmentId } = req.params;
    const studentId = req.user._id;

    const assessment = await Assessment.findOne({
        _id: assessmentId,
        'studentMarks.student': studentId
    })
        .populate('batch', 'batchId courseName students')
        .populate('uploadedBy', 'firstName lastName');

    if (!assessment) {
        throw new ApiError(404, 'Assessment result not found');
    }

    // Check if student is enrolled in the batch
    if (!assessment.batch.students.includes(studentId)) {
        throw new ApiError(403, 'Access denied to this assessment result');
    }

    const myMark = assessment.studentMarks.find(
        mark => mark.student.toString() === studentId.toString()
    );

    // Calculate class statistics
    const allMarks = assessment.studentMarks.map(mark => mark.marks);
    const classAverage = allMarks.length > 0 ?
        allMarks.reduce((sum, mark) => sum + mark, 0) / allMarks.length : 0;
    const highestMarks = allMarks.length > 0 ? Math.max(...allMarks) : 0;
    const lowestMarks = allMarks.length > 0 ? Math.min(...allMarks) : 0;

    // Calculate rank
    const sortedMarks = [...allMarks].sort((a, b) => b - a);
    const myRank = myMark ? sortedMarks.indexOf(myMark.marks) + 1 : null;

    const result = {
        assessment: {
            _id: assessment._id,
            title: assessment.title,
            type: assessment.type,
            subject: assessment.subject,
            date: assessment.date,
            maxMarks: assessment.maxMarks
        },
        batch: assessment.batch,
        trainer: assessment.uploadedBy,
        myResult: {
            marks: myMark ? myMark.marks : 0,
            percentage: myMark ? Math.round((myMark.marks / assessment.maxMarks) * 100) : 0,
            grade: myMark ? myMark.grade : 'N/A',
            remarks: myMark ? myMark.remarks : '',
            rank: myRank
        },
        classStatistics: {
            totalStudents: assessment.studentMarks.length,
            averageMarks: Math.round(classAverage * 100) / 100,
            averagePercentage: Math.round((classAverage / assessment.maxMarks) * 100),
            highestMarks,
            lowestMarks,
            highestPercentage: Math.round((highestMarks / assessment.maxMarks) * 100),
            lowestPercentage: Math.round((lowestMarks / assessment.maxMarks) * 100)
        }
    };

    res.status(200).json(new ApiResponse(200, result, 'Assessment result retrieved successfully'));
});

const getMyProgress = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { batchId } = req.query;

    // Get enrolled batches
    const enrolledBatchesFilter = { students: studentId };
    if (batchId) enrolledBatchesFilter._id = batchId;

    const batches = await Batch.find(enrolledBatchesFilter)
        .populate('courseId', 'title')
        .populate('trainerId', 'firstName lastName');

    const progressData = await Promise.all(
        batches.map(async (batch) => {
            // Materials progress
            const totalMaterials = await CourseMaterial.countDocuments({ batch: batch._id });

            // Assignment progress
            const totalAssignments = await Assignment.countDocuments({ batch: batch._id });
            const submittedAssignments = await AssignmentSubmission.countDocuments({
                student: studentId,
                assignment: { $in: await Assignment.distinct('_id', { batch: batch._id }) }
            });

            // Assessment progress
            const totalAssessments = await Assessment.countDocuments({ batch: batch._id });
            const completedAssessments = await Assessment.countDocuments({
                batch: batch._id,
                'studentMarks.student': studentId
            });

            // Attendance progress
            const attendanceStats = await Attendance.aggregate([
                { $match: { batchId: batch._id, studentId: studentId } },
                {
                    $group: {
                        _id: null,
                        totalClasses: { $sum: 1 },
                        attendedClasses: { $sum: { $cond: ['$status', 1, 0] } }
                    }
                }
            ]);

            const attendance = attendanceStats.length > 0 ? attendanceStats[0] :
                { totalClasses: 0, attendedClasses: 0 };

            // Average assessment score
            const assessmentResults = await Assessment.find({
                batch: batch._id,
                'studentMarks.student': studentId
            });

            const averageScore = assessmentResults.length > 0 ?
                assessmentResults.reduce((sum, assessment) => {
                    const myMark = assessment.studentMarks.find(
                        mark => mark.student.toString() === studentId.toString()
                    );
                    return sum + (myMark ? (myMark.marks / assessment.maxMarks) * 100 : 0);
                }, 0) / assessmentResults.length : 0;

            return {
                batch: {
                    _id: batch._id,
                    batchId: batch.batchId,
                    courseName: batch.courseName,
                    course: batch.courseId,
                    trainer: batch.trainerId,
                    status: batch.status
                },
                progress: {
                    materials: {
                        total: totalMaterials,
                        accessed: totalMaterials // Assume all materials are accessible
                    },
                    assignments: {
                        total: totalAssignments,
                        submitted: submittedAssignments,
                        percentage: totalAssignments > 0 ?
                            Math.round((submittedAssignments / totalAssignments) * 100) : 100
                    },
                    assessments: {
                        total: totalAssessments,
                        completed: completedAssessments,
                        percentage: totalAssessments > 0 ?
                            Math.round((completedAssessments / totalAssessments) * 100) : 100,
                        averageScore: Math.round(averageScore)
                    },
                    attendance: {
                        totalClasses: attendance.totalClasses,
                        attendedClasses: attendance.attendedClasses,
                        percentage: attendance.totalClasses > 0 ?
                            Math.round((attendance.attendedClasses / attendance.totalClasses) * 100) : 100
                    }
                }
            };
        })
    );
    res.status(200).json(new ApiResponse(200, progressData, 'Progress data retrieved successfully'));
});
// ================================
// FEEDBACK
// ================================
const getMyFeedback = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { page = 1, limit = 10 } = req.query;
    const feedback = await Feedback.find({ student: studentId })
        .populate('trainer', 'firstName lastName')
        .populate('batch', 'batchId courseName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Feedback.countDocuments({ student: studentId });

    res.status(200).json(new ApiResponse(200, {
        feedback,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Feedback retrieved successfully'));
});

const submitFeedback = asyncHandler(async (req, res) => {
    const { trainerId, batchId, rating, comments, suggestions, anonymous = false } = req.body;
    const studentId = req.user._id;

    // Verify student is enrolled in the batch
    const batch = await Batch.findOne({ _id: batchId, students: studentId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not enrolled');
    }

    // Verify trainer is assigned to this batch
    if (batch.trainerId.toString() !== trainerId.toString()) {
        throw new ApiError(400, 'Trainer is not assigned to this batch');
    }

    // Check if feedback already exists
    const existingFeedback = await Feedback.findOne({
        student: studentId,
        trainer: trainerId,
        batch: batchId
    });

    if (existingFeedback) {
        throw new ApiError(400, 'Feedback already submitted for this trainer and batch');
    }

    const feedback = await Feedback.create({
        student: anonymous ? null : studentId,
        trainer: trainerId,
        batch: batchId,
        rating: parseInt(rating),
        comments,
        suggestions,
        anonymous,
        submittedBy: studentId // Keep track of who submitted even if anonymous
    });

    const populatedFeedback = await Feedback.findById(feedback._id)
        .populate('trainer', 'firstName lastName')
        .populate('batch', 'batchId courseName')
        .populate('student', 'firstName lastName');

    // Send notification to trainer
    await Notification.create({
        title: 'New Feedback Received',
        message: anonymous ?
            'You have received anonymous feedback' :
            `You have received feedback from ${req.user.firstName} ${req.user.lastName}`,
        type: 'feedback',
        recipients: [trainerId],
        createdBy: studentId,
        relatedId: feedback._id
    });

    res.status(201).json(new ApiResponse(201, populatedFeedback, 'Feedback submitted successfully'));
});

const getFeedbackDetails = asyncHandler(async (req, res) => {
    const { feedbackId } = req.params;
    const studentId = req.user._id;

    const feedback = await Feedback.findOne({
        _id: feedbackId,
        $or: [
            { student: studentId },
            { submittedBy: studentId }
        ]
    })
        .populate('trainer', 'firstName lastName')
        .populate('batch', 'batchId courseName')
        .populate('student', 'firstName lastName');

    if (!feedback) {
        throw new ApiError(404, 'Feedback not found');
    }
    res.status(200).json(new ApiResponse(200, feedback, 'Feedback details retrieved successfully'));
});

const updateFeedback = asyncHandler(async (req, res) => {
    const { feedbackId } = req.params;
    const { rating, comments, suggestions } = req.body;
    const studentId = req.user._id;

    const feedback = await Feedback.findOne({
        _id: feedbackId,
        submittedBy: studentId
    });

    if (!feedback) {
        throw new ApiError(404, 'Feedback not found or access denied');
    }

    // Update allowed fields
    if (rating !== undefined) feedback.rating = parseInt(rating);
    if (comments !== undefined) feedback.comments = comments;
    if (suggestions !== undefined) feedback.suggestions = suggestions;

    feedback.updatedAt = new Date();
    await feedback.save();

    const populatedFeedback = await Feedback.findById(feedback._id)
        .populate('trainer', 'firstName lastName')
        .populate('batch', 'batchId courseName');
    res.status(200).json(new ApiResponse(200, populatedFeedback, 'Feedback updated successfully'));
});

// ================================
// CERTIFICATES
// ================================
const getMyCertificates = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { status, page = 1, limit = 10 } = req.query;

    const filter = { student: studentId };
    if (status) filter.status = status;

    const certificates = await Certificate.find(filter)
        .populate('batch', 'batchId courseName')
        .populate('issuedBy', 'firstName lastName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Certificate.countDocuments(filter);

    res.status(200).json(new ApiResponse(200, {
        certificates,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Certificates retrieved successfully'));
});

const requestCertificate = asyncHandler(async (req, res) => {
    const { batchId, certificateType = 'completion' } = req.body;
    const studentId = req.user._id;

    // Verify student is enrolled in the batch
    const batch = await Batch.findOne({ _id: batchId, students: studentId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not enrolled');
    }

    // Check if batch is completed or student has completed requirements
    if (batch.status !== 'completed') {
        // Check completion criteria (attendance, assignments, etc.)
        const attendanceStats = await Attendance.aggregate([
            { $match: { batchId: new mongoose.Types.ObjectId(batchId), studentId: studentId } },
            {
                $group: {
                    _id: null,
                    totalClasses: { $sum: 1 },
                    attendedClasses: { $sum: { $cond: ['$status', 1, 0] } }
                }
            }
        ]);

        const attendance = attendanceStats.length > 0 ? attendanceStats[0] :
            { totalClasses: 0, attendedClasses: 0 };
        const attendancePercentage = attendance.totalClasses > 0 ?
            (attendance.attendedClasses / attendance.totalClasses) * 100 : 0;

        if (attendancePercentage < 75) { // Minimum 75% attendance required
            throw new ApiError(400, 'Minimum 75% attendance required for certificate');
        }
    }

    // Check if certificate already requested
    const existingRequest = await Certificate.findOne({
        student: studentId,
        batch: batchId,
        certificateType
    });

    if (existingRequest) {
        throw new ApiError(400, 'Certificate already requested for this batch');
    }

    const certificate = await Certificate.create({
        student: studentId,
        batch: batchId,
        certificateType,
        status: 'pending',
        requestedAt: new Date()
    });

    const populatedCertificate = await Certificate.findById(certificate._id)
        .populate('batch', 'batchId courseName')
        .populate('student', 'firstName lastName');

    // Send notification to admin/trainer
    await Notification.create({
        title: 'Certificate Request',
        message: `${req.user.firstName} ${req.user.lastName} has requested a ${certificateType} certificate for ${batch.courseName}`,
        type: 'certificate_request',
        recipients: [batch.trainerId], // Or admin IDs
        createdBy: studentId,
        relatedId: certificate._id
    });

    res.status(201).json(new ApiResponse(201, populatedCertificate, 'Certificate requested successfully'));
});

const getCertificateDetails = asyncHandler(async (req, res) => {
    const { certificateId } = req.params;
    const studentId = req.user._id;

    const certificate = await Certificate.findOne({
        _id: certificateId,
        student: studentId
    })
        .populate('batch', 'batchId courseName startDate endDate')
        .populate('issuedBy', 'firstName lastName')
        .populate('student', 'firstName lastName');

    if (!certificate) {
        throw new ApiError(404, 'Certificate not found');
    }

    res.status(200).json(new ApiResponse(200, certificate, 'Certificate details retrieved successfully'));
});

const downloadCertificate = asyncHandler(async (req, res) => {
    const { certificateId } = req.params;
    const studentId = req.user._id;

    const certificate = await Certificate.findOne({
        _id: certificateId,
        student: studentId,
        status: 'issued'
    });

    if (!certificate) {
        throw new ApiError(404, 'Certificate not found or not yet issued');
    }

    if (!certificate.certificateUrl) {
        throw new ApiError(400, 'Certificate file not available');
    }

    // Increment download count
    certificate.downloadCount = (certificate.downloadCount || 0) + 1;
    await certificate.save();

    res.status(200).json(new ApiResponse(200, {
        downloadUrl: certificate.certificateUrl,
        fileName: `Certificate_${certificate.certificateId || certificate._id}.pdf`
    }, 'Certificate download link generated successfully'));
});

// ================================
// NOTIFICATIONS
// ================================
const getNotifications = asyncHandler(async (req, res) => {
    const studentId = req.user._id;
    const { type, read, page = 1, limit = 10 } = req.query;

    const filter = { recipients: studentId };
    if (type) filter.type = type;
    if (read !== undefined) {
        if (read === 'true') {
            filter.readBy = studentId;
        } else {
            filter.readBy = { $ne: studentId };
        }
    }

    const notifications = await Notification.find(filter)
        .populate('batch', 'batchId courseName')
        .populate('createdBy', 'firstName lastName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Notification.countDocuments(filter);
    const unreadCount = await Notification.countDocuments({
        recipients: studentId,
        readBy: { $ne: studentId }
    });

    // Mark notifications as read in response
    const notificationsWithReadStatus = notifications.map(notification => ({
        ...notification.toObject(),
        isRead: notification.readBy.includes(studentId)
    }));

    res.status(200).json(new ApiResponse(200, {
        notifications: notificationsWithReadStatus,
        unreadCount,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Notifications retrieved successfully'));
});

const markNotificationAsRead = asyncHandler(async (req, res) => {
    const { notificationId } = req.params;
    const studentId = req.user._id;

    const notification = await Notification.findOne({
        _id: notificationId,
        recipients: studentId
    });

    if (!notification) {
        throw new ApiError(404, 'Notification not found');
    }

    // Add student to readBy array if not already present
    if (!notification.readBy.includes(studentId)) {
        notification.readBy.push(studentId);
        await notification.save();
    }

    res.status(200).json(new ApiResponse(200, null, 'Notification marked as read'));
});

const markAllNotificationsAsRead = asyncHandler(async (req, res) => {
    const studentId = req.user._id;

    await Notification.updateMany(
        {
            recipients: studentId,
            readBy: { $ne: studentId }
        },
        {
            $push: { readBy: studentId }
        }
    );

    res.status(200).json(new ApiResponse(200, null, 'All notifications marked as read'));
});

// ================================
// PROFILE MANAGEMENT
// ================================
const getProfile = asyncHandler(async (req, res) => {
    const studentId = req.user._id;

    const student = await User.findById(studentId)
        .select('-password')
        .populate('batchId', 'batchId courseName startDate endDate status');

    if (!student) {
        throw new ApiError(404, 'Student profile not found');
    }

    // Get additional statistics
    const enrolledBatches = await Batch.find({ students: studentId }).countDocuments();
    const totalAssignments = await Assignment.countDocuments({
        batch: { $in: await Batch.find({ students: studentId }).distinct('_id') }
    });
    const submittedAssignments = await AssignmentSubmission.countDocuments({
        student: studentId
    });

    const overallAttendance = await Attendance.aggregate([
        { $match: { studentId: studentId } },
        {
            $group: {
                _id: null,
                totalClasses: { $sum: 1 },
                attendedClasses: { $sum: { $cond: ['$status', 1, 0] } }
            }
        }
    ]);

    const attendance = overallAttendance.length > 0 ? overallAttendance[0] :
        { totalClasses: 0, attendedClasses: 0 };

    const profileData = {
        ...student.toObject(),
        statistics: {
            enrolledBatches,
            totalAssignments,
            submittedAssignments,
            assignmentCompletionRate: totalAssignments > 0 ?
                Math.round((submittedAssignments / totalAssignments) * 100) : 0,
            overallAttendance: attendance.totalClasses > 0 ?
                Math.round((attendance.attendedClasses / attendance.totalClasses) * 100) : 0,
            totalClasses: attendance.totalClasses
        }
    };

    res.status(200).json(new ApiResponse(200, profileData, 'Profile retrieved successfully'));
});

module.exports = {
    getDashboard,
    getEnrolledBatches,
    getBatchDetails,
    getCourseDetails,
    getBatchMaterials,
    getMaterialDetails,
    downloadMaterial,
    getBatchAssignments,
    getAssignmentDetails,
    submitAssignment,
    getMySubmission,
    updateSubmission,
    getMyAttendance,
    getBatchAttendance,
    getAttendanceSummary,
    getMyResults,
    getBatchResults,
    getAssessmentResult,
    getMyProgress,
    getMyFeedback,
    submitFeedback,
    getFeedbackDetails,
    updateFeedback,
    getMyCertificates,
    requestCertificate,
    getCertificateDetails,
    downloadCertificate,
    getNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    getProfile,
    updateProfile,
    getPerformanceReport,
    getAttendanceReport
};