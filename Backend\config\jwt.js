const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const logger = require('../utils/logger');

// JWT Configuration
const jwtConfig = {
    accessTokenSecret: process.env.JWT_ACCESS_SECRET || crypto.randomBytes(64).toString('hex'),
    refreshTokenSecret: process.env.JWT_REFRESH_SECRET || crypto.randomBytes(64).toString('hex'),
    accessTokenExpire: process.env.JWT_ACCESS_EXPIRE || '15m',
    refreshTokenExpire: process.env.JWT_REFRESH_EXPIRE || '7d',
    algorithm: 'HS256'
};

// Generate Access Token
const generateAccessToken = (payload) => {
    try {
        return jwt.sign(payload, jwtConfig.accessTokenSecret, {
            expiresIn: jwtConfig.accessTokenExpire,
            algorithm: jwtConfig.algorithm,
            issuer: 'college-management-system',
            audience: 'college-management-users'
        });
    } catch (error) {
        logger.error('Error generating access token:', error);
        throw new Error('Token generation failed');
    }
};

// Generate Refresh Token
const generateRefreshToken = (payload) => {
    try {
        return jwt.sign(payload, jwtConfig.refreshTokenSecret, {
            expiresIn: jwtConfig.refreshTokenExpire,
            algorithm: jwtConfig.algorithm,
            issuer: 'college-management-system',
            audience: 'college-management-users'
        });
    } catch (error) {
        logger.error('Error generating refresh token:', error);
        throw new Error('Token generation failed');
    }
};

// Generate Token Pair
const generateTokenPair = (payload) => {
    try {
        const accessToken = generateAccessToken(payload);
        const refreshToken = generateRefreshToken(payload);

        return {
            accessToken,
            refreshToken,
            tokenType: 'Bearer',
            expiresIn: jwtConfig.accessTokenExpire
        };
    } catch (error) {
        logger.error('Error generating token pair:', error);
        throw new Error('Token generation failed');
    }
};

// Verify Access Token
const verifyAccessToken = (token) => {
    try {
        return jwt.verify(token, jwtConfig.accessTokenSecret, {
            algorithms: [jwtConfig.algorithm],
            issuer: 'college-management-system',
            audience: 'college-management-users'
        });
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            throw new Error('Access token expired');
        } else if (error.name === 'JsonWebTokenError') {
            throw new Error('Invalid access token');
        } else {
            logger.error('Error verifying access token:', error);
            throw new Error('Token verification failed');
        }
    }
};

// Verify Refresh Token
const verifyRefreshToken = (token) => {
    try {
        return jwt.verify(token, jwtConfig.refreshTokenSecret, {
            algorithms: [jwtConfig.algorithm],
            issuer: 'college-management-system',
            audience: 'college-management-users'
        });
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            throw new Error('Refresh token expired');
        } else if (error.name === 'JsonWebTokenError') {
            throw new Error('Invalid refresh token');
        } else {
            logger.error('Error verifying refresh token:', error);
            throw new Error('Token verification failed');
        }
    }
};

// Decode Token without verification (for debugging)
const decodeToken = (token) => {
    try {
        return jwt.decode(token, { complete: true });
    } catch (error) {
        logger.error('Error decoding token:', error);
        return null;
    }
};

// Get Token Expiration
const getTokenExpiration = (token) => {
    try {
        const decoded = jwt.decode(token);
        return decoded ? new Date(decoded.exp * 1000) : null;
    } catch (error) {
        logger.error('Error getting token expiration:', error);
        return null;
    }
};

// Check if token is expired
const isTokenExpired = (token) => {
    try {
        const expiration = getTokenExpiration(token);
        return expiration ? Date.now() >= expiration.getTime() : true;
    } catch (error) {
        logger.error('Error checking token expiration:', error);
        return true;
    }
};

// Generate Password Reset Token
const generatePasswordResetToken = (payload) => {
    try {
        return jwt.sign(payload, jwtConfig.accessTokenSecret, {
            expiresIn: '1h', // Password reset tokens expire in 1 hour
            algorithm: jwtConfig.algorithm,
            issuer: 'college-management-system',
            audience: 'password-reset'
        });
    } catch (error) {
        logger.error('Error generating password reset token:', error);
        throw new Error('Token generation failed');
    }
};

// Verify Password Reset Token
const verifyPasswordResetToken = (token) => {
    try {
        return jwt.verify(token, jwtConfig.accessTokenSecret, {
            algorithms: [jwtConfig.algorithm],
            issuer: 'college-management-system',
            audience: 'password-reset'
        });
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            throw new Error('Password reset token expired');
        } else if (error.name === 'JsonWebTokenError') {
            throw new Error('Invalid password reset token');
        } else {
            logger.error('Error verifying password reset token:', error);
            throw new Error('Token verification failed');
        }
    }
};

// Generate Email Verification Token
const generateEmailVerificationToken = (payload) => {
    try {
        return jwt.sign(payload, jwtConfig.accessTokenSecret, {
            expiresIn: '24h', // Email verification tokens expire in 24 hours
            algorithm: jwtConfig.algorithm,
            issuer: 'college-management-system',
            audience: 'email-verification'
        });
    } catch (error) {
        logger.error('Error generating email verification token:', error);
        throw new Error('Token generation failed');
    }
};

// Verify Email Verification Token
const verifyEmailVerificationToken = (token) => {
    try {
        return jwt.verify(token, jwtConfig.accessTokenSecret, {
            algorithms: [jwtConfig.algorithm],
            issuer: 'college-management-system',
            audience: 'email-verification'
        });
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            throw new Error('Email verification token expired');
        } else if (error.name === 'JsonWebTokenError') {
            throw new Error('Invalid email verification token');
        } else {
            logger.error('Error verifying email verification token:', error);
            throw new Error('Token verification failed');
        }
    }
};

// Extract token from Authorization header
const extractTokenFromHeader = (authHeader) => {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
    }
    return authHeader.substring(7);
};

// Create token blacklist (in production, use Redis)
const tokenBlacklist = new Set();

// Add token to blacklist
const blacklistToken = (token) => {
    tokenBlacklist.add(token);
};

// Check if token is blacklisted
const isTokenBlacklisted = (token) => {
    return tokenBlacklist.has(token);
};

// Clear expired tokens from blacklist (should be called periodically)
const clearExpiredBlacklistedTokens = () => {
    const now = Date.now();
    for (const token of tokenBlacklist) {
        if (isTokenExpired(token)) {
            tokenBlacklist.delete(token);
        }
    }
};

module.exports = {
    jwtConfig,
    generateAccessToken,
    generateRefreshToken,
    generateTokenPair,
    verifyAccessToken,
    verifyRefreshToken,
    decodeToken,
    getTokenExpiration,
    isTokenExpired,
    generatePasswordResetToken,
    verifyPasswordResetToken,
    generateEmailVerificationToken,
    verifyEmailVerificationToken,
    extractTokenFromHeader,
    blacklistToken,
    isTokenBlacklisted,
    clearExpiredBlacklistedTokens
};