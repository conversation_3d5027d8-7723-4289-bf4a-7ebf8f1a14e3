const multer = require('multer');
const path = require('path');
const ApiError = require('../utils/ApiError');
const { v2: cloudinary } = require('cloudinary');

// Configure storage
const storage = multer.memoryStorage();

// File filter function
const fileFilter = (req, file, cb) => {
    // Define allowed file types
    const allowedTypes = {
        'course-materials': [
            'application/pdf',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
            'image/gif',
            'video/mp4',
            'video/avi',
            'audio/mpeg',
            'audio/wav',
            'text/plain'
        ],
        'assignments': [
            'application/pdf',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/zip',
            'application/x-zip-compressed'
        ],
        'submissions': [
            'application/pdf',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/zip',
            'application/x-zip-compressed',
            'image/jpeg',
            'image/png'
        ]
    };

    // Determine file type category based on route
    let category = 'course-materials';
    if (req.route.path.includes('assignment')) {
        category = 'assignments';
    } else if (req.route.path.includes('submission')) {
        category = 'submissions';
    }

    // Check if file type is allowed
    if (allowedTypes[category].includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new ApiError(400, `File type ${file.mimetype} is not allowed for ${category}`), false);
    }
};

// Configure multer
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 50 * 1024 * 1024, // 50MB limit
        files: 1 // Single file upload
    },
    fileFilter: fileFilter
});

// Error handling middleware for multer
const handleMulterError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                throw new ApiError(400, 'File size too large. Maximum size is 50MB');
            case 'LIMIT_FILE_COUNT':
                throw new ApiError(400, 'Too many files. Only one file is allowed');
            case 'LIMIT_UNEXPECTED_FILE':
                throw new ApiError(400, 'Unexpected file field');
            default:
                throw new ApiError(400, 'File upload error: ' + error.message);
        }
    }
    next(error);
};

// File validation helper
const validateFile = (req, res, next) => {
    if (!req.file && req.route.methods.post) {
        // Check if route requires file upload
        const requiresFile = [
            '/batches/:batchId/materials',
            '/assignments/:assignmentId/submit'
        ].some(pattern => req.route.path.includes(pattern.split('/').pop()));

        if (requiresFile) {
            throw new ApiError(400, 'Please upload a file');
        }
    }

    if (req.file) {
        // Additional file validation
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (req.file.size > maxSize) {
            throw new ApiError(400, 'File size too large. Maximum size is 50MB');
        }

        // Validate file extension
        const allowedExtensions = [
            '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.txt',
            '.jpg', '.jpeg', '.png', '.gif', '.mp4', '.avi',
            '.mp3', '.wav', '.zip'
        ];

        const fileExtension = path.extname(req.file.originalname).toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            throw new ApiError(400, `File extension ${fileExtension} is not allowed`);
        }

        // Add file metadata
        req.file.uploadPath = generateUploadPath(req);
    }

    next();
};

// Generate upload path based on file type and user
const generateUploadPath = (req) => {
    const userId = req.user._id;
    const timestamp = Date.now();
    const fileExtension = path.extname(req.file.originalname);

    let folder = 'misc';
    if (req.route.path.includes('material')) {
        folder = 'course-materials';
    } else if (req.route.path.includes('assignment')) {
        folder = 'assignments';
    } else if (req.route.path.includes('submission')) {
        folder = 'submissions';
    }

    return `${folder}/${userId}/${timestamp}${fileExtension}`;
};
const cloudinaryBufferUpload = async (buffer, folder = 'uploads', resourceType = 'auto') => {
    return new Promise((resolve, reject) => {
        const uploadStream = cloudinary.uploader.upload_stream(
            {
                folder,
                resource_type: resourceType,
                public_id: `material_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
            },
            (error, result) => {
                if (error) return reject(new ApiError(500, 'Cloudinary upload failed'));
                resolve(result);
            }
        );
        uploadStream.end(buffer);
    });
}
// Export configured upload middleware
module.exports = {
    upload,
    cloudinaryBufferUpload,
    handleMulterError,
    validateFile,
    single: (fieldName) => [
        upload.single(fieldName),
        handleMulterError,
        validateFile
    ],
    array: (fieldName, maxCount = 5) => [
        upload.array(fieldName, maxCount),
        handleMulterError,
        validateFile
    ]
};